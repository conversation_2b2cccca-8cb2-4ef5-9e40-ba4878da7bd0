"use client";

import Link from "next/link";
import { User } from "@/utils/types";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Settings, LogOut, Home, Shield } from "lucide-react";
import { signOut } from "@/lib/auth-client";

interface AdminHeaderProps {
  user: User;
  onMenuClick?: () => void;
}

export default function AdminHeader({ user, onMenuClick }: AdminHeaderProps) {
  if (!user) {
    return null; // or a spinner/placeholder if preferred
  }
  return (
    <header className="bg-white border-b border-gray-200 px-2 sm:px-6 py-3 min-w-0 overflow-x-auto">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between min-w-0 overflow-x-auto gap-2 sm:gap-0">
        <div className="flex items-center gap-2 sm:gap-4">
          {/* Hamburger menu for mobile */}
          {onMenuClick && (
            <button
              className="lg:hidden mr-2 p-2 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
              onClick={onMenuClick}
              aria-label="Open sidebar menu"
            >
              <svg className="h-6 w-6 text-gray-700" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          )}
          <Link href="/admin" className="flex items-center gap-1 sm:gap-2">
            <Shield className="h-5 w-5 sm:h-6 sm:w-6 text-blue-600" />
            <h1 className="text-lg sm:text-xl font-bold text-gray-900">Rivv Admin</h1>
          </Link>
        </div>
        <div className="flex items-center gap-2 sm:gap-4 flex-wrap justify-start sm:justify-end mt-2 sm:mt-0">
          <Link href="/dashboard">
            <Button variant="outline" size="sm">
              <Home className="h-4 w-4 mr-2" />
              Back to Store
            </Button>
          </Link>
          <DropdownMenu>
            <DropdownMenuTrigger>
              <div className="flex items-center gap-1 sm:gap-2">
                <Avatar className="hover:cursor-pointer h-8 w-8 sm:h-9 sm:w-9">
                  <AvatarImage src={user.image || ""} />
                  <AvatarFallback>{user.name.charAt(0).toUpperCase()}</AvatarFallback>
                </Avatar>
                <Badge variant="secondary" className="text-xs">
                  Admin
                </Badge>
              </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuLabel>
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-medium leading-none">{user.name}</p>
                  <p className="text-xs leading-none text-muted-foreground">
                    {user.email}
                  </p>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem asChild>
                <Link href="/profile" className="flex items-center">
                  <Settings className="mr-2 h-4 w-4" />
                  Profile Settings
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href="/dashboard" className="flex items-center">
                  <Home className="mr-2 h-4 w-4" />
                  Back to Store
                </Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                className="flex items-center text-red-600 focus:text-red-600"
                onClick={async () => {
                  await signOut({
                    fetchOptions: {
                      onSuccess: () => {
                        window.location.href = "/";
                      },
                    },
                  });
                }}
              >
                <LogOut className="mr-2 h-4 w-4" />
                Sign Out
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
}
