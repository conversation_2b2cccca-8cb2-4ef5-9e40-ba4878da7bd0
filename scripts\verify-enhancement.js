require('dotenv').config();
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function verifyEnhancement() {
  try {
    const product = await prisma.product.findUnique({
      where: { id: 'cmcngr5ca000bkz4c0g8bx89m' },
      select: {
        name: true,
        enhancementStatus: true,
        qualityScore: true,
        imageSource: true,
        lastEnhancedAt: true,
        aiAnalysis: true,
        imageAngles: true
      }
    });
    
    if (product) {
      console.log('✅ Product Enhancement Verification:');
      console.log('   Name:', product.name);
      console.log('   Enhancement Status:', product.enhancementStatus);
      console.log('   Quality Score:', product.qualityScore);
      console.log('   Image Source:', product.imageSource);
      console.log('   Last Enhanced:', product.lastEnhancedAt);
      console.log('   AI Analysis:', product.aiAnalysis ? 'Available' : 'None');
      console.log('   Image Angles:', product.imageAngles ? Object.keys(product.imageAngles) : 'None');
    } else {
      console.log('❌ Product not found');
    }
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

verifyEnhancement();
