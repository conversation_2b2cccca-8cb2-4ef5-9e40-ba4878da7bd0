#!/usr/bin/env node

/**
 * Show Dashboard Statistics
 * Displays the current enhancement statistics that the admin dashboard shows
 */

const axios = require('axios');

async function showDashboardStats() {
  console.log('📊 Rivv AI Enhancement Dashboard Statistics');
  console.log('==========================================\n');

  try {
    const response = await axios.get('http://localhost:3000/api/admin/enhancement-stats');
    
    if (response.data.success) {
      const { stats, recentLogs } = response.data.data;
      
      // Display main statistics
      console.log('📈 Overall Statistics:');
      console.log(`   Total Products: ${stats.total}`);
      console.log(`   ✅ Completed: ${stats.completed}`);
      console.log(`   ⚠️  Review Required: ${stats.review_required}`);
      console.log(`   🔄 Processing: ${stats.processing}`);
      console.log(`   ❌ Failed: ${stats.failed}`);
      console.log(`   ⏳ Pending: ${stats.pending}`);
      console.log(`   🖼️  With Images: ${stats.with_images}`);
      console.log(`   📊 Average Quality Score: ${stats.averageQualityScore}%`);
      
      // Calculate completion percentage
      const completionRate = ((stats.completed / stats.total) * 100).toFixed(1);
      console.log(`   📈 Completion Rate: ${completionRate}%`);
      
      console.log('\n🎯 Enhancement Status Breakdown:');
      const statusData = [
        { label: 'Completed', count: stats.completed, emoji: '✅' },
        { label: 'Review Required', count: stats.review_required, emoji: '⚠️' },
        { label: 'Processing', count: stats.processing, emoji: '🔄' },
        { label: 'Failed', count: stats.failed, emoji: '❌' },
        { label: 'Pending', count: stats.pending, emoji: '⏳' }
      ];
      
      statusData.forEach(status => {
        const percentage = ((status.count / stats.total) * 100).toFixed(1);
        const bar = '█'.repeat(Math.floor(status.count / stats.total * 20));
        console.log(`   ${status.emoji} ${status.label}: ${status.count} (${percentage}%) ${bar}`);
      });
      
      // Recent activity
      console.log('\n📋 Recent Enhancement Activity:');
      if (recentLogs.length > 0) {
        recentLogs.slice(0, 10).forEach((log, index) => {
          const date = new Date(log.updatedAt).toLocaleString();
          const statusEmoji = {
            'completed': '✅',
            'review_required': '⚠️',
            'processing': '🔄',
            'failed': '❌',
            'pending': '⏳'
          }[log.status] || '❓';
          
          console.log(`   ${index + 1}. ${statusEmoji} Product ${log.id.substring(0, 8)}... - ${log.status} (Score: ${log.qualityScore || 'N/A'}) - ${date}`);
        });
      } else {
        console.log('   No recent activity found.');
      }
      
      // Quality distribution
      console.log('\n🎨 Quality Score Analysis:');
      const qualityRanges = {
        'Excellent (90-100%)': recentLogs.filter(l => l.qualityScore >= 90).length,
        'Good (80-89%)': recentLogs.filter(l => l.qualityScore >= 80 && l.qualityScore < 90).length,
        'Fair (70-79%)': recentLogs.filter(l => l.qualityScore >= 70 && l.qualityScore < 80).length,
        'Poor (<70%)': recentLogs.filter(l => l.qualityScore < 70).length
      };
      
      Object.entries(qualityRanges).forEach(([range, count]) => {
        if (count > 0) {
          console.log(`   ${range}: ${count} products`);
        }
      });
      
      // Recommendations
      console.log('\n💡 Recommendations:');
      if (stats.review_required > 0) {
        console.log(`   • Review ${stats.review_required} products marked for manual review`);
        console.log('   • Go to: http://localhost:3000/admin/image-enhancement');
      }
      
      if (stats.pending > 0) {
        console.log(`   • Process ${stats.pending} pending products`);
        console.log('   • Run: pnpm run enhance-comprehensive');
      }
      
      if (stats.failed > 0) {
        console.log(`   • Investigate ${stats.failed} failed enhancements`);
        console.log('   • Run: node scripts/enhance-all-products-comprehensive.js --filter=failed');
      }
      
      if (stats.averageQualityScore < 85) {
        console.log('   • Consider re-enhancing with new AI system for better quality');
        console.log('   • Run: pnpm run re-enhance-low-quality');
      }
      
      if (stats.completed === stats.total) {
        console.log('   🎉 All products have been enhanced! Your system is fully optimized.');
      }
      
    } else {
      console.log('❌ Failed to fetch dashboard statistics');
    }
    
  } catch (error) {
    console.error('❌ Error fetching dashboard stats:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('   • Make sure your Next.js server is running: pnpm dev');
    console.log('   • Check if the API endpoint is accessible');
  }
}

showDashboardStats().catch(console.error);
