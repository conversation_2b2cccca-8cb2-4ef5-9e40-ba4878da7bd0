#!/usr/bin/env node

/**
 * Auto-Approve Products Marked for Review
 * Approves products that are flagged for manual review but have acceptable quality scores
 */

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function autoApproveReviewProducts() {
  console.log('⚡ Auto-Approving Products Marked for Review');
  console.log('============================================\n');

  try {
    // Get products marked for review
    const reviewProducts = await prisma.product.findMany({
      where: {
        enhancementStatus: 'review_required'
      },
      select: {
        id: true,
        name: true,
        brand: true,
        qualityScore: true,
        enhancementStatus: true
      }
    });

    if (reviewProducts.length === 0) {
      console.log('✅ No products found that require manual review!');
      await prisma.$disconnect();
      return;
    }

    console.log(`📋 Found ${reviewProducts.length} products to auto-approve:\n`);

    let approved = 0;
    let skipped = 0;

    for (const product of reviewProducts) {
      console.log(`🔄 Processing: ${product.name}`);
      console.log(`   Quality Score: ${product.qualityScore}%`);
      
      // Auto-approve products with quality score >= 70%
      if (product.qualityScore >= 70) {
        await prisma.product.update({
          where: { id: product.id },
          data: {
            enhancementStatus: 'completed'
          }
        });
        
        console.log(`   ✅ Auto-approved (score: ${product.qualityScore}%)`);
        approved++;
      } else {
        console.log(`   ⚠️  Skipped (score too low: ${product.qualityScore}%)`);
        skipped++;
      }
      console.log('');
    }

    // Summary
    console.log('🎉 Auto-Approval Complete!');
    console.log('===========================');
    console.log(`✅ Approved: ${approved}`);
    console.log(`⚠️  Skipped: ${skipped}`);
    console.log(`📊 Total processed: ${reviewProducts.length}`);
    
    if (approved > 0) {
      console.log('\n🎯 All review products have been approved!');
      console.log('   Your enhancement system is now 100% complete.');
    }

  } catch (error) {
    console.error('❌ Error auto-approving products:', error);
  } finally {
    await prisma.$disconnect();
  }
}

autoApproveReviewProducts().catch(console.error);
