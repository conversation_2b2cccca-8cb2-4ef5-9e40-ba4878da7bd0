import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth-utils';
import prisma from '@/lib/prisma';
import { spawn } from 'child_process';
import path from 'path';

export async function POST(request: NextRequest) {
  try {
    // Check authentication and admin role
    const user = await getCurrentUser();
    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 401 }
      );
    }

    // Get count of failed products
    const failedCount = await prisma.product.count({
      where: {
        isActive: true,
        images: { isEmpty: false },
        enhancementStatus: 'failed'
      }
    });

    if (failedCount === 0) {
      return NextResponse.json({
        success: true,
        message: 'No failed enhancements to retry',
        failedCount: 0
      });
    }

    // Reset failed products to pending status
    await prisma.product.updateMany({
      where: {
        isActive: true,
        images: { isEmpty: false },
        enhancementStatus: 'failed'
      },
      data: {
        enhancementStatus: null // Reset to null so they get picked up again
      }
    });

    // Start the enhancement process for failed products
    const scriptPath = path.join(process.cwd(), 'scripts/enhance-existing-products.js');
    
    try {
      const child = spawn('node', [scriptPath, '--batch-size=5', '--force'], {
        detached: true,
        stdio: 'ignore',
        cwd: process.cwd(),
        env: { ...process.env }
      });

      // Detach the process so it continues running
      child.unref();

      return NextResponse.json({
        success: true,
        message: `Retrying ${failedCount} failed enhancements`,
        failedCount,
        processId: child.pid
      });

    } catch (spawnError) {
      console.error('Error spawning retry process:', spawnError);
      return NextResponse.json(
        { error: 'Failed to start retry process' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error retrying failed enhancements:', error);
    return NextResponse.json(
      { error: 'Failed to retry failed enhancements' },
      { status: 500 }
    );
  }
}
