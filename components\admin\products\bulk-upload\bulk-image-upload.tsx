"use client";

import { useState, useCallback, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import {
  FileImage,
  X,
  CheckCircle,
  AlertCircle,
  Loader2,
  Trash2,
  ImagePlus,
  Check,
  XCircle,
  Image as ImageIcon,
  UploadCloud,
  Smartphone,
  Tablet,
  Monitor,
  Info
} from "lucide-react";
import { UploadedImage } from "./bulk-upload-content";
import { UploadDropzone } from "@/lib/uploadthing";
import { cn } from "@/lib/utils";
import { useIsMobile, useIsTablet } from "@/hooks/use-media-query";

interface BulkImageUploadProps {
  onImagesUploaded: (images: UploadedImage[]) => void;
  uploadedImages: UploadedImage[];
}

interface UploadProgress {
  [key: string]: number;
}

// Add device indicator component
const DeviceIndicator = ({ deviceType }: { deviceType: 'mobile' | 'tablet' | 'desktop' | null }) => {
  if (!deviceType) return null;
  
  const deviceIcons = {
    mobile: <Smartphone className="w-4 h-4" />,
    tablet: <Tablet className="w-4 h-4" />,
    desktop: <Monitor className="w-4 h-4" />
  };

  return (
    <div className="flex items-center gap-1 text-xs text-muted-foreground bg-muted px-2 py-1 rounded-full">
      {deviceIcons[deviceType]}
      <span className="capitalize">{deviceType}</span>
    </div>
  );
};

export default function BulkImageUpload({ onImagesUploaded, uploadedImages }: BulkImageUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<UploadProgress>({});
  const [uploadErrors, setUploadErrors] = useState<string[]>([]);
  const [selectedImages, setSelectedImages] = useState<Set<string>>(new Set());
  const [isDragging, setIsDragging] = useState(false);
  const [showMobileTips, setShowMobileTips] = useState(false);
  const [deviceType, setDeviceType] = useState<'mobile' | 'tablet' | 'desktop' | null>(null);
  
  // Detect device type
  const isMobile = useIsMobile();
  const isTablet = useIsTablet();
  
  useEffect(() => {
    if (isMobile) {
      setDeviceType('mobile');
      setShowMobileTips(true);
    } else if (isTablet) {
      setDeviceType('tablet');
    } else {
      setDeviceType('desktop');
    }
  }, [isMobile, isTablet]);

  const handleUploadComplete = useCallback((res: any[]) => {
    if (res && res.length > 0) {
      const newImages: UploadedImage[] = res.map(file => ({
        id: `${Date.now()}-${Math.random()}`,
        url: file.ufsUrl,
        name: file.name,
        size: file.size,
        assigned: false,
      }));

      const allImages = [...uploadedImages, ...newImages];
      onImagesUploaded(allImages);
      setUploadProgress({});
      setUploadErrors([]);
      
      // Show success toast
      toast.success(`Successfully uploaded ${res.length} image(s)`, {
        description: 'You can now assign these images to products.'
      });
    }
    setIsUploading(false);
  }, [uploadedImages, onImagesUploaded]);

  const handleUploadError = useCallback((error: Error) => {
    console.error("Upload error:", error);
    const errorMessage = error.message.includes('File type not permitted') 
      ? 'Only JPG, PNG, and WebP files are supported.'
      : error.message;
      
    setUploadErrors(prev => [...prev, errorMessage]);
    setIsUploading(false);
    setUploadProgress({});
    
    // Show error toast
    toast.error('Upload failed', {
      description: errorMessage
    });
  }, []);

  const removeImage = (imageId: string) => {
    const updatedImages = uploadedImages.filter(img => img.id !== imageId);
    onImagesUploaded(updatedImages);
    setSelectedImages(prev => {
      const newSet = new Set(prev);
      newSet.delete(imageId);
      return newSet;
    });
  };

  const toggleImageSelection = (imageId: string) => {
    setSelectedImages(prev => {
      const newSet = new Set(prev);
      if (newSet.has(imageId)) {
        newSet.delete(imageId);
      } else {
        newSet.add(imageId);
      }
      return newSet;
    });
  };

  const removeSelectedImages = () => {
    const updatedImages = uploadedImages.filter(img => !selectedImages.has(img.id));
    onImagesUploaded(updatedImages);
    setSelectedImages(new Set());
  };

  const selectAllImages = () => {
    if (selectedImages.size === uploadedImages.length) {
      setSelectedImages(new Set());
    } else {
      setSelectedImages(new Set(uploadedImages.map(img => img.id)));
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Handle drag and drop events
  const handleDragEnter = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      // The UploadDropzone will handle the file upload
      // We just need to trigger the file input click
      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
      if (fileInput) {
        fileInput.files = e.dataTransfer.files;
        fileInput.dispatchEvent(new Event('change', { bubbles: true }));
      }
    }
  };

  return (
    <div className="space-y-6">
      {/* Upload Instructions */}
      <div className="text-center">
        <div className="relative inline-block">
          <FileImage className="w-12 h-12 text-primary/80 mx-auto mb-4" />
          {deviceType && <DeviceIndicator deviceType={deviceType} />}
        </div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Upload Product Images</h2>
        <p className="text-gray-600 mb-4">
          {isMobile 
            ? 'Select multiple images from your gallery or take new photos.'
            : 'Drag & drop images here or click to browse. Supported formats: JPEG, PNG, WebP'}
        </p>
        
        {isMobile && (
          <Button 
            variant="outline" 
            className="mb-4"
            onClick={() => setShowMobileTips(!showMobileTips)}
            size="sm"
          >
            {showMobileTips ? 'Hide Tips' : 'Show Mobile Tips'}
          </Button>
        )}
        
        {showMobileTips && isMobile && (
          <div className="bg-blue-50 p-4 rounded-lg text-left text-sm text-blue-800 mb-4">
            <h4 className="font-semibold mb-2 flex items-center">
              <Smartphone className="w-4 h-4 mr-2" />
              Mobile Upload Tips:
            </h4>
            <ul className="list-disc pl-5 space-y-1">
              <li>Tap to select multiple images from your gallery</li>
              <li>Use the camera option to take new photos</li>
              <li>You can select up to 20 images at once</li>
              <li>Make sure images are well-lit and in focus</li>
            </ul>
          </div>
        )}
      </div>

      {/* Upload Dropzone */}
      <Card 
        className={cn(
          "border-2 border-dashed transition-colors relative overflow-hidden",
          isDragging 
            ? "border-primary bg-primary/5" 
            : "border-gray-300 hover:border-gray-400"
        )}
        onDragEnter={handleDragEnter}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        {/* Drag and drop overlay */}
        {isDragging && (
          <div className="absolute inset-0 bg-primary/5 flex items-center justify-center z-10">
            <div className="text-center p-6 bg-white rounded-lg shadow-lg border border-primary/20">
              <UploadCloud className="w-12 h-12 text-primary mx-auto mb-4" />
              <p className="text-lg font-medium text-gray-900">Drop your images here</p>
              <p className="text-sm text-gray-500 mt-1">Upload will begin when you release the files</p>
            </div>
          </div>
        )}
        
        <CardContent className="p-4 sm:p-6 md:p-8">
          <UploadDropzone
            endpoint="bulkProductImageUploader"
            onClientUploadComplete={handleUploadComplete}
            onUploadError={handleUploadError}
            onUploadBegin={() => setIsUploading(true)}
            onUploadProgress={(progress) => {
              // UploadThing returns a number, but we need an object
              setUploadProgress({ 'upload': progress });
            }}
            appearance={{
              container: "w-full border-none",
              uploadIcon: "text-gray-400",
              label: "text-gray-600 text-lg",
              allowedContent: "text-gray-500",
              button: cn(
                "bg-primary text-primary-foreground hover:bg-primary/90 px-6 py-2 rounded-lg font-medium transition-colors",
                "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
              ),
            }}
            content={{
              label: isMobile 
                ? "Tap to select images" 
                : "Drag & drop images here or click to browse",
              allowedContent: isMobile 
                ? "JPG, PNG, WebP • Max 20MB" 
                : "Images up to 4MB each • Max 100 files • JPG, PNG, WebP",
            }}

          />
        </CardContent>
      </Card>

      {/* Upload Progress */}
      {isUploading && (
        <Card>
          <CardContent className="p-4">
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <Loader2 className="w-5 h-5 animate-spin text-primary" />
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-sm font-medium text-foreground">
                      Uploading {Object.keys(uploadProgress).length} file(s)...
                    </span>
                    <span className="text-xs text-muted-foreground">
                      {Math.max(...Object.values(uploadProgress), 0)}%
                    </span>
                  </div>
                  <Progress 
                    value={Math.max(...Object.values(uploadProgress), 0)} 
                    className="h-2 w-full" 
                  />
                </div>
              </div>
              
              {Object.entries(uploadProgress).length > 0 && (
                <div className="mt-2 space-y-1 max-h-40 overflow-y-auto pr-2">
                  {Object.entries(uploadProgress).map(([fileName, progress]) => (
                    <div key={fileName} className="flex items-center text-xs">
                      <span className="w-8 text-muted-foreground">{progress}%</span>
                      <span className="truncate flex-1">{fileName}</span>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Upload Errors */}
      {uploadErrors.length > 0 && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-1">
              {uploadErrors.map((error, index) => (
                <div key={index}>{error}</div>
              ))}
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Uploaded Images */}
      {uploadedImages.length > 0 && (
        <div className="space-y-4">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
            <div className="flex items-center space-x-2 sm:space-x-4">
              <h3 className="text-lg font-semibold text-foreground">
                Uploaded Images ({uploadedImages.length})
              </h3>
              <Badge variant="outline" className="h-6">
                {selectedImages.size} selected
              </Badge>
            </div>
            <div className="flex items-center space-x-2 overflow-x-auto pb-1">
              <Button
                variant="outline"
                size="sm"
                onClick={selectAllImages}
                className="whitespace-nowrap"
              >
                {selectedImages.size === uploadedImages.length ? 'Deselect All' : 'Select All'}
              </Button>
              
              {selectedImages.size > 0 && (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={removeSelectedImages}
                    className="text-destructive hover:text-destructive/90 hover:bg-destructive/5"
                  >
                    <Trash2 className="w-4 h-4 mr-1.5" />
                    <span>Remove ({selectedImages.size})</span>
                  </Button>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      // TODO: Implement batch assign functionality
                      toast.info('Batch assign coming soon!');
                    }}
                    disabled={selectedImages.size === 0}
                    className="text-blue-600 hover:text-blue-700"
                  >
                    <Check className="w-4 h-4 mr-1.5" />
                    <span>Assign ({selectedImages.size})</span>
                  </Button>
                </>
              )}
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  // TODO: Implement organize functionality
                  toast.info('Organize images feature coming soon!');
                }}
                className="hidden sm:flex"
              >
                <span>Organize</span>
              </Button>
            </div>
          </div>

          <div className="relative grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 2xl:grid-cols-7 gap-3 sm:gap-4">
            {uploadedImages.map((image) => (
              <Card 
                key={image.id} 
                className={cn(
                  "relative group cursor-pointer transition-all overflow-hidden",
                  "border border-border/70 hover:border-primary/50",
                  selectedImages.has(image.id) 
                    ? 'ring-2 ring-primary/50 bg-primary/5' 
                    : 'hover:shadow-sm'
                )}
                onClick={() => toggleImageSelection(image.id)}
              >
                <CardContent className="p-2">
                  <div className="aspect-square relative rounded-md bg-muted/50 overflow-hidden">
                    {/* Thumbnail */}
                    <div className="w-full h-full flex items-center justify-center">
                      <img
                        src={image.url}
                        alt={image.name}
                        className={cn(
                          "w-full h-full object-cover transition-transform duration-200",
                          selectedImages.has(image.id) ? 'scale-105' : 'group-hover:scale-105'
                        )}
                        loading="lazy"
                      />
                    </div>
                    
                    {/* Selection Checkbox */}
                    <div className={cn(
                      "absolute top-2 right-2 w-5 h-5 rounded-full border-2 transition-all flex items-center justify-center",
                      selectedImages.has(image.id)
                        ? 'bg-primary border-primary'
                        : 'bg-background/80 border-border/70 group-hover:border-primary/70',
                    )}>
                      {selectedImages.has(image.id) && (
                        <Check className="w-3 h-3 text-white" strokeWidth={3} />
                      )}
                    </div>

                    {/* Image Actions */}
                    <div className={cn(
                      "absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100",
                      "flex items-end p-2 transition-opacity duration-200"
                    )}>
                      <div className="w-full flex justify-between items-center">
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-7 w-7 bg-background/80 hover:bg-background"
                          onClick={(e) => {
                            e.stopPropagation();
                            // TODO: Implement preview functionality
                            toast.info('Image preview coming soon!');
                          }}
                        >
                          <ImageIcon className="w-3.5 h-3.5" />
                        </Button>
                        
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-7 w-7 bg-background/80 hover:bg-background text-destructive hover:text-destructive"
                          onClick={(e) => {
                            e.stopPropagation();
                            removeImage(image.id);
                          }}
                        >
                          <X className="w-3.5 h-3.5" />
                        </Button>
                      </div>
                    </div>

                    {/* Assignment Status */}
                    {image.assigned && (
                      <Badge 
                        variant="secondary" 
                        className="absolute bottom-2 left-2 text-xs font-medium px-1.5 py-0.5"
                      >
                        Assigned
                      </Badge>
                    )}
                  </div>
                  
                  {/* Image Info */}
                  <div className="mt-2 space-y-0.5 px-0.5">
                    <p className="text-xs font-medium text-foreground truncate">
                      {image.name.split('.').slice(0, -1).join('.')}
                    </p>
                    <div className="flex justify-between items-center">
                      <span className="text-[10px] text-muted-foreground">
                        {formatFileSize(image.size)}
                      </span>
                      <span className="text-[10px] text-muted-foreground">
                        {image.url.split('.').pop()?.toUpperCase()}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
            
            {uploadedImages.length === 0 && (
              <div className="col-span-full py-12 flex flex-col items-center justify-center text-center">
                <ImageIcon className="w-10 h-10 text-muted-foreground/50 mb-3" />
                <h4 className="text-sm font-medium text-muted-foreground">
                  No images uploaded yet
                </h4>
                <p className="text-xs text-muted-foreground/70 mt-1 max-w-xs">
                  Upload product images or drag and drop them here
                </p>
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 justify-between items-start sm:items-center pt-4 border-t border-border/50">
            <div className="text-sm text-muted-foreground">
              {uploadedImages.length > 0 ? (
                <>
                  <span className="font-medium">{uploadedImages.length}</span> image{uploadedImages.length !== 1 ? 's' : ''} selected
                  {Object.values(uploadProgress).length > 0 && (
                    <span className="text-green-600 ml-2">
                      {Math.round(
                        (Object.values(uploadProgress).reduce((a, b) => a + b, 0) / 
                        (Object.values(uploadProgress).length * 100)) * 100
                      )}% uploaded
                    </span>
                  )}
                </>
              ) : (
                <span>No images uploaded yet</span>
              )}
            </div>
            
            <div className="flex flex-col sm:flex-row gap-3 w-full sm:w-auto">
              <Button
                variant="outline"
                onClick={() => {
                  // TODO: Implement save as draft functionality
                  toast.info('Draft saved successfully!');
                }}
                className="w-full sm:w-auto"
              >
                Save as Draft
              </Button>
              
              <Button
                onClick={() => onImagesUploaded(uploadedImages)}
                disabled={uploadedImages.length === 0}
                className="w-full sm:w-auto"
              >
                Continue to Product Details
                <span className="ml-2 hidden sm:inline">
                  ({uploadedImages.length} image{uploadedImages.length !== 1 ? 's' : ''})
                </span>
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
