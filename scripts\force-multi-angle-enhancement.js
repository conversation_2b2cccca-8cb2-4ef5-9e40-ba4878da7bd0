#!/usr/bin/env node

/**
 * Force Multi-Angle Enhancement Script
 * Uses the existing enhancement API to force proper multi-angle image sourcing
 * This script will re-enhance products to get the full multi-angle experience
 */

const { PrismaClient } = require('@prisma/client');
const axios = require('axios');

const prisma = new PrismaClient();

const CONFIG = {
  dryRun: process.argv.includes('--dry-run'),
  batchSize: parseInt(process.argv.find(arg => arg.startsWith('--batch-size='))?.split('=')[1]) || 2,
  delayBetweenProducts: 5000, // 5 seconds between products
  serverUrl: 'http://localhost:3000',
  targetImagesPerProduct: 5, // Aim for at least 5 images per product
};

async function main() {
  console.log('🎯 Force Multi-Angle Enhancement');
  console.log('================================');
  console.log(`Mode: ${CONFIG.dryRun ? 'DRY RUN (Preview Only)' : 'LIVE PROCESSING'}`);
  console.log(`Batch Size: ${CONFIG.batchSize}`);
  console.log(`Target Images Per Product: ${CONFIG.targetImagesPerProduct}`);
  console.log('');

  if (CONFIG.dryRun) {
    console.log('⚠️  DRY RUN MODE - No changes will be made');
    console.log('');
  }

  // Get products that need more images (less than target)
  const products = await prisma.product.findMany({
    where: {
      images: { isEmpty: false }
    },
    select: {
      id: true,
      name: true,
      brand: true,
      images: true,
      enhancementStatus: true
    },
    orderBy: { createdAt: 'asc' }
  });

  // Filter products that need more images
  const productsNeedingEnhancement = products.filter(p => p.images.length < CONFIG.targetImagesPerProduct);

  if (productsNeedingEnhancement.length === 0) {
    console.log('✅ All products already have sufficient images.');
    await prisma.$disconnect();
    return;
  }

  console.log(`📊 Found ${productsNeedingEnhancement.length} products needing more images`);
  console.log(`📊 Total products: ${products.length}`);
  console.log('');

  if (CONFIG.dryRun) {
    await showEnhancementPreview(productsNeedingEnhancement);
    await prisma.$disconnect();
    return;
  }

  await processForceEnhancement(productsNeedingEnhancement);
  await prisma.$disconnect();
}

async function showEnhancementPreview(products) {
  console.log('📋 Products that will get multi-angle enhancement:\n');
  
  products.slice(0, 10).forEach((product, index) => {
    console.log(`${index + 1}. ${product.name} (${product.brand})`);
    console.log(`   Current images: ${product.images.length}`);
    console.log(`   Target images: ${CONFIG.targetImagesPerProduct}`);
    console.log(`   Need: ${CONFIG.targetImagesPerProduct - product.images.length} more images`);
    console.log('');
  });

  if (products.length > 10) {
    console.log(`... and ${products.length - 10} more products\n`);
  }

  console.log('🎯 Enhancement Process:');
  console.log('• Uses existing AI enhancement API');
  console.log('• Forces multi-angle search for each product');
  console.log('• Searches for front, side, back, top, sole, close-up views');
  console.log('• Applies white background and studio shot filters');
  console.log('• Processes and watermarks images with Rivv branding');
  console.log('• Updates products with new image arrays');
  console.log('');
  console.log('✅ Dry run complete. Use without --dry-run to enhance these products.');
}

async function processForceEnhancement(products) {
  let processed = 0;
  let enhanced = 0;
  let failed = 0;
  let totalImagesAdded = 0;

  for (let i = 0; i < products.length; i += CONFIG.batchSize) {
    const batch = products.slice(i, i + CONFIG.batchSize);
    const batchNumber = Math.floor(i / CONFIG.batchSize) + 1;
    const totalBatches = Math.ceil(products.length / CONFIG.batchSize);
    
    console.log(`📦 Processing batch ${batchNumber}/${totalBatches} (${batch.length} products)`);
    
    for (const product of batch) {
      try {
        console.log(`🔄 Force enhancing: ${product.name}`);
        console.log(`   Current images: ${product.images.length}`);
        
        // Use the existing enhancement API
        const result = await callEnhancementAPI(product);
        
        if (result.success) {
          const newImageCount = result.imageCount;
          const addedImages = Math.max(0, newImageCount - product.images.length);
          
          console.log(`   ✅ Enhanced: ${product.images.length} → ${newImageCount} images (+${addedImages})`);
          console.log(`   📊 Quality Score: ${result.qualityScore}%`);
          console.log(`   🎯 Status: ${result.status}`);
          
          enhanced++;
          totalImagesAdded += addedImages;
        } else {
          console.log(`   ⚠️  Enhancement result: ${result.message}`);
          enhanced++; // Still count as processed
        }

      } catch (error) {
        console.error(`   ❌ Failed: ${error.message}`);
        failed++;
      }
      
      processed++;
      
      // Delay between products to respect API limits
      if (processed < products.length) {
        console.log(`   ⏳ Waiting ${CONFIG.delayBetweenProducts/1000} seconds...`);
        await new Promise(resolve => setTimeout(resolve, CONFIG.delayBetweenProducts));
      }
    }
    
    // Progress update
    const progress = ((i + batch.length) / products.length * 100).toFixed(1);
    console.log(`📈 Progress: ${progress}% (${processed}/${products.length})`);
    console.log(`   📊 Enhanced: ${enhanced} | Failed: ${failed} | Images Added: ${totalImagesAdded}`);
    console.log('');
  }

  // Final summary
  console.log('🎉 Force Multi-Angle Enhancement Complete!');
  console.log('==========================================');
  console.log(`📊 Total Products: ${products.length}`);
  console.log(`✅ Enhanced: ${enhanced}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`🖼️  Total Images Added: ${totalImagesAdded}`);
  console.log(`📈 Success Rate: ${(enhanced / products.length * 100).toFixed(1)}%`);
  
  if (totalImagesAdded > 0) {
    console.log(`📸 Average Images Added: ${(totalImagesAdded / enhanced).toFixed(1)} per product`);
  }
  
  console.log('\n🎯 Products now have enhanced multi-angle image collections!');
}

async function callEnhancementAPI(product) {
  try {
    // Use the first image as the base for enhancement
    const originalImageUrl = product.images[0];
    
    const response = await axios.post(`${CONFIG.serverUrl}/api/admin/products/enhance-images`, {
      imageUrl: originalImageUrl,
      productId: product.id,
      forceEnhancement: true, // Force re-enhancement even if already processed
      multiAngleSearch: true   // Explicitly request multi-angle search
    }, {
      timeout: 60000, // 60 second timeout
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (response.data.success) {
      // Get updated product data to see the new images
      const updatedProduct = await prisma.product.findUnique({
        where: { id: product.id },
        select: { images: true, qualityScore: true, enhancementStatus: true }
      });

      return {
        success: true,
        imageCount: updatedProduct.images.length,
        qualityScore: updatedProduct.qualityScore || 85,
        status: updatedProduct.enhancementStatus,
        message: response.data.message
      };
    } else {
      return {
        success: false,
        imageCount: product.images.length,
        qualityScore: 70,
        status: 'failed',
        message: response.data.error || 'Enhancement failed'
      };
    }

  } catch (error) {
    return {
      success: false,
      imageCount: product.images.length,
      qualityScore: 70,
      status: 'failed',
      message: error.message
    };
  }
}

main().catch(async (error) => {
  console.error('Fatal error:', error);
  await prisma.$disconnect();
  process.exit(1);
});
