import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/auth-utils";
import { enhanceProductImages } from "@/utils/advanced-image-processor";
import prisma from "@/lib/prisma";

/**
 * POST /api/admin/products/enhance-images
 * AI-Powered Product Image Enhancement API
 * Implements the 6-step enhancement logic for Rivv e-commerce platform
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication and admin role
    const user = await getCurrentUser();
    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { 
      originalImageUrl, 
      productId,
      autoApprove = false,
      confidenceThreshold = 80 
    } = body;

    // Validate required fields
    if (!originalImageUrl) {
      return NextResponse.json(
        { error: 'Missing required field: originalImageUrl' },
        { status: 400 }
      );
    }

    console.log(`🚀 Starting AI enhancement for image: ${originalImageUrl}`);

    // Run the complete 6-step enhancement process
    const enhancementResult = await enhanceProductImages(originalImageUrl, productId);

    // Use advanced confidence scoring for auto-approval decision
    const autoApprovalDecision = enhancementResult.auto_approval_decision;
    const shouldAutoApprove = autoApprove &&
      autoApprovalDecision?.shouldAutoApprove &&
      autoApprovalDecision.riskLevel === 'low' &&
      enhancementResult.images.length > 0;

    // If productId provided and auto-approval enabled, update the product
    if (productId && shouldAutoApprove) {
      try {
        const imageUrls = enhancementResult.images.map(img => img.url);
        
        await prisma.product.update({
          where: { id: productId },
          data: {
            images: imageUrls,
            enhancementStatus: 'completed',
            qualityScore: enhancementResult.confidence_score
          }
        });

        console.log(`✅ Auto-approved and updated product ${productId} with ${imageUrls.length} enhanced images`);
        
        return NextResponse.json({
          success: true,
          data: {
            ...enhancementResult,
            auto_approved: true,
            product_updated: true,
            confidence_analysis: {
              decision: autoApprovalDecision?.recommendedAction,
              risk_level: autoApprovalDecision?.riskLevel,
              factors: autoApprovalDecision?.factors,
              reasoning: autoApprovalDecision?.reasoning
            }
          },
          message: `Successfully enhanced and updated product with ${enhancementResult.images.length} high-quality images (Auto-approved with ${enhancementResult.confidence_score}% confidence)`
        });
        
      } catch (dbError) {
        console.error('Database update failed:', dbError);
        // Continue with manual approval flow
      }
    }

    // Manual approval required or auto-approval failed
    const response = {
      success: true,
      data: {
        ...enhancementResult,
        auto_approved: false,
        requires_manual_review: autoApprovalDecision?.recommendedAction !== 'auto_approve',
        confidence_threshold: confidenceThreshold,
        confidence_analysis: {
          decision: autoApprovalDecision?.recommendedAction,
          risk_level: autoApprovalDecision?.riskLevel,
          factors: autoApprovalDecision?.factors,
          reasoning: autoApprovalDecision?.reasoning,
          detailed_scores: {
            product_identification: autoApprovalDecision?.factors.productIdentification,
            image_quality: autoApprovalDecision?.factors.imageQuality,
            source_reliability: autoApprovalDecision?.factors.sourceReliability,
            angle_completeness: autoApprovalDecision?.factors.angleCompleteness,
            brand_recognition: autoApprovalDecision?.factors.brandRecognition,
            consistency: autoApprovalDecision?.factors.consistencyScore
          }
        }
      },
      message: enhancementResult.images.length > 0
        ? `Found ${enhancementResult.images.length} enhanced images. ${autoApprovalDecision?.recommendedAction === 'auto_approve' ? 'Auto-approval conditions not met.' : `${autoApprovalDecision?.recommendedAction?.replace('_', ' ')} recommended.`}`
        : 'Enhancement completed but no suitable images found. Original image was processed as fallback.'
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Error in image enhancement API:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Image enhancement failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/admin/products/enhance-images
 * Get enhancement status and configuration
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication and admin role
    const user = await getCurrentUser();
    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 401 }
      );
    }

    // Get configuration and stats
    const config = {
      confidence_threshold: 80,
      auto_approval_enabled: true,
      supported_angles: ['front', 'side_left', 'side_right', 'back', 'top', 'sole', 'close_up'],
      quality_requirements: {
        min_resolution: '1000x1000',
        background_score_min: 85,
        clarity_score_min: 75,
        composition_score_min: 80,
        overall_score_min: 80
      },
      processing_steps: [
        '1. Product Identification (Gemini AI)',
        '2. Online Image Search (Google Custom Search)',
        '3. Strict Quality Filtering',
        '4. Multi-Angle Organization',
        '5. Image Processing & Branding',
        '6. Structured JSON Output'
      ]
    };

    // Get recent enhancement stats
    const recentEnhancements = await prisma.product.count({
      where: {
        enhancementStatus: 'completed',
        updatedAt: {
          gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // Last 7 days
        }
      }
    });

    const pendingEnhancements = await prisma.product.count({
      where: {
        enhancementStatus: 'processing'
      }
    });

    return NextResponse.json({
      success: true,
      data: {
        config,
        stats: {
          recent_enhancements: recentEnhancements,
          pending_enhancements: pendingEnhancements
        }
      }
    });

  } catch (error) {
    console.error('Error getting enhancement config:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to get enhancement configuration' 
      },
      { status: 500 }
    );
  }
}
