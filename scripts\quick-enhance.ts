#!/usr/bin/env ts-node

/**
 * Quick Enhancement Script for Rivv Products
 * Simple script to enhance a few products at a time
 * 
 * Usage:
 * pnpm exec ts-node scripts/quick-enhance.ts
 */

import prisma from '../lib/prisma';
import { enhanceProductImages } from '../utils/advanced-image-processor';

async function quickEnhance() {
  console.log('🚀 Rivv Quick Image Enhancement');
  console.log('===============================\n');

  // Get first 5 products that need enhancement
  const products = await prisma.product.findMany({
    where: {
      AND: [
        { images: { isEmpty: false } },
        { 
          OR: [
            { enhancementStatus: null },
            { enhancementStatus: 'pending' },
            { enhancementStatus: 'failed' }
          ]
        }
      ]
    },
    take: 5,
    select: {
      id: true,
      name: true,
      brand: true,
      images: true,
      enhancementStatus: true
    }
  });

  if (products.length === 0) {
    console.log('✅ No products found that need enhancement!');
    return;
  }

  console.log(`📦 Found ${products.length} products to enhance:\n`);
  
  products.forEach((product, index) => {
    console.log(`${index + 1}. ${product.name} (${product.brand})`);
  });

  console.log('\n🔄 Starting enhancement...\n');

  let processed = 0;
  let autoApproved = 0;
  let manualReview = 0;
  let failed = 0;

  for (const product of products) {
    try {
      console.log(`🔄 Processing: ${product.name}`);
      
      // Mark as processing
      await prisma.product.update({
        where: { id: product.id },
        data: { enhancementStatus: 'processing' }
      });

      // Run enhancement
      const result = await enhanceProductImages(product.images[0], product.id);
      
      // Check auto-approval
      const shouldAutoApprove = result.auto_approval_decision?.shouldAutoApprove &&
                               result.auto_approval_decision.riskLevel === 'low' &&
                               result.confidence_score >= 80;

      if (shouldAutoApprove && result.images.length > 0) {
        // Auto-approve
        await prisma.product.update({
          where: { id: product.id },
          data: {
            images: result.images.map(img => img.url),
            enhancementStatus: 'completed',
            qualityScore: result.confidence_score
          }
        });
        
        console.log(`✅ Auto-approved: ${result.images.length} images (${result.confidence_score}% confidence)`);
        autoApproved++;
        
      } else if (result.images.length > 0) {
        // Manual review needed
        await prisma.product.update({
          where: { id: product.id },
          data: {
            enhancementStatus: 'review_required',
            qualityScore: result.confidence_score
          }
        });
        
        console.log(`⚠️  Manual review needed (${result.confidence_score}% confidence)`);
        manualReview++;
        
      } else {
        // Failed
        await prisma.product.update({
          where: { id: product.id },
          data: { enhancementStatus: 'failed' }
        });
        
        console.log(`❌ No suitable images found`);
        failed++;
      }
      
      processed++;
      console.log('');
      
    } catch (error) {
      console.error(`❌ Error: ${error}`);
      failed++;
      processed++;
    }
  }

  // Summary
  console.log('🎉 Quick Enhancement Complete!');
  console.log('==============================');
  console.log(`✅ Auto-approved: ${autoApproved}`);
  console.log(`⚠️  Manual review: ${manualReview}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📊 Total processed: ${processed}`);
  
  if (manualReview > 0) {
    console.log('\n📋 Next: Check admin dashboard for manual review items');
  }
}

quickEnhance().catch(console.error).finally(() => process.exit(0));
