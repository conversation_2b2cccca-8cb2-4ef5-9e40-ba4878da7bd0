/**
 * Advanced Product Image Processing System
 * Implements sophisticated online search, filtering, and multi-angle image retrieval
 */

import { GoogleGenerativeAI } from "@google/generative-ai";
import sharp from 'sharp';
import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';
import { uploadToS3 } from './storage';
import path from 'path';
import fs from 'fs';

// Initialize Gemini AI
const genAI = new GoogleGenerativeAI(process.env.GOOGLE_AI_API_KEY!);
const visionModel = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });

// Product identification interface
interface ProductMetadata {
  productType: string;
  brand: string;
  modelName: string;
  colorway: string;
  sku?: string;
  confidence: number;
}

// Image quality assessment interface
interface ImageQualityScore {
  backgroundClarity: number; // 0-100 (white background = 100)
  foreignElements: number;   // 0-100 (no hands/feet/models = 100)
  sharpness: number;        // 0-100
  resolution: number;       // 0-100
  centering: number;        // 0-100
  overall: number;          // 0-100
}

// Enhanced image result interface
interface EnhancedImageResult {
  angle: string;
  url: string;
  qualityScore: ImageQualityScore;
  source: 'web_fetch' | 'enhanced_original' | '360_rotation';
  confidence: number;
}

// Final processing result
interface ProcessingResult {
  product_id: string;
  product_name: string;
  image_source: 'web_fetch' | 'enhanced_original';
  images: EnhancedImageResult[];
  rotation_url?: string;
  metadata: ProductMetadata;
  processing_notes: string[];
}

/**
 * Step 1: Extract product metadata using Gemini AI
 */
export async function identifyProduct(imageUrl: string): Promise<ProductMetadata> {
  const prompt = `Analyze this product image and extract detailed metadata.
  
  Return a JSON object with:
  {
    "productType": "sneaker|hoodie|cap|shirt|pants|jacket|bag|accessory",
    "brand": "exact brand name (Nike, Adidas, etc.)",
    "modelName": "specific model name",
    "colorway": "color description",
    "sku": "any visible SKU or product code",
    "confidence": "confidence score 0-100"
  }
  
  Be precise with brand names and model identification.`;

  try {
    const result = await visionModel.generateContent([
      { text: prompt },
      { fileData: { mimeType: 'image/jpeg', fileUri: imageUrl } }
    ]);

    const response = await result.response;
    const text = response.text();
    
    const metadata = JSON.parse(text.replace(/```json\n?|\n?```/g, ''));
    return metadata;
  } catch (error) {
    console.error('Error identifying product:', error);
    return {
      productType: 'unknown',
      brand: 'unknown',
      modelName: 'unknown',
      colorway: 'unknown',
      confidence: 0
    };
  }
}

/**
 * Step 2: Initiate sophisticated online search
 */
export async function searchProductImages(metadata: ProductMetadata): Promise<Array<{
  url: string;
  width: number;
  height: number;
  source: string;
  angle?: string;
}>> {
  const searchQueries = [
    // Primary search with all metadata
    `${metadata.brand} ${metadata.modelName} ${metadata.colorway} white background studio shot product image`,
    // Secondary search without colorway
    `${metadata.brand} ${metadata.modelName} white background product photography`,
    // Tertiary search with product type
    `${metadata.brand} ${metadata.modelName} ${metadata.productType} official product image`,
    // Angle-specific searches
    `${metadata.brand} ${metadata.modelName} side view white background`,
    `${metadata.brand} ${metadata.modelName} front view product shot`,
    `${metadata.brand} ${metadata.modelName} back view studio photography`,
    `${metadata.brand} ${metadata.modelName} top view overhead shot`,
    `${metadata.brand} ${metadata.modelName} sole bottom view`,
    `${metadata.brand} ${metadata.modelName} detail close up texture`
  ];

  let allResults: Array<{url: string; width: number; height: number; source: string; angle?: string}> = [];

  for (const query of searchQueries) {
    try {
      const results = await searchImagesOnline(query);
      allResults = allResults.concat(results);
    } catch (error) {
      console.error(`Search failed for query: ${query}`, error);
    }
  }

  // Remove duplicates
  const uniqueResults = allResults.filter((item, index, self) => 
    index === self.findIndex(t => t.url === item.url)
  );

  return uniqueResults;
}

/**
 * Step 3: Strict image filtering with AI quality assessment
 */
export async function filterAndScoreImages(
  images: Array<{url: string; width: number; height: number; source: string}>,
  metadata: ProductMetadata
): Promise<Array<{url: string; qualityScore: ImageQualityScore; angle?: string}>> {
  const filteredImages: Array<{url: string; qualityScore: ImageQualityScore; angle?: string}> = [];

  for (const image of images) {
    try {
      // Skip low resolution images
      if (image.width < 800 || image.height < 800) continue;

      const qualityScore = await assessImageQuality(image.url, metadata);
      
      // Apply strict filtering criteria
      if (qualityScore.overall >= 70 && 
          qualityScore.backgroundClarity >= 80 && 
          qualityScore.foreignElements >= 70) {
        
        const angle = await detectImageAngle(image.url, metadata.productType);
        filteredImages.push({
          url: image.url,
          qualityScore,
          angle
        });
      }
    } catch (error) {
      console.error(`Error processing image ${image.url}:`, error);
    }
  }

  // Sort by quality score
  return filteredImages.sort((a, b) => b.qualityScore.overall - a.qualityScore.overall);
}

/**
 * Assess image quality using AI
 */
async function assessImageQuality(imageUrl: string, metadata: ProductMetadata): Promise<ImageQualityScore> {
  const prompt = `Analyze this product image for quality assessment.
  
  Rate each aspect from 0-100:
  - backgroundClarity: Is the background pure white/clean? (100 = perfect white, 0 = cluttered)
  - foreignElements: Are there hands, feet, models, shelves, floors? (100 = product only, 0 = many distractions)
  - sharpness: Is the image sharp and clear? (100 = crystal clear, 0 = very blurry)
  - resolution: Is the image high resolution? (100 = very high res, 0 = pixelated)
  - centering: Is the product centered and well-framed? (100 = perfectly centered, 0 = off-center)
  
  Return JSON: {"backgroundClarity": 0-100, "foreignElements": 0-100, "sharpness": 0-100, "resolution": 0-100, "centering": 0-100, "overall": 0-100}`;

  try {
    const result = await visionModel.generateContent([
      { text: prompt },
      { fileData: { mimeType: 'image/jpeg', fileUri: imageUrl } }
    ]);

    const response = await result.response;
    const text = response.text();
    
    const scores = JSON.parse(text.replace(/```json\n?|\n?```/g, ''));
    
    // Calculate overall score
    scores.overall = Math.round(
      (scores.backgroundClarity * 0.3 + 
       scores.foreignElements * 0.25 + 
       scores.sharpness * 0.2 + 
       scores.resolution * 0.15 + 
       scores.centering * 0.1)
    );

    return scores;
  } catch (error) {
    console.error('Error assessing image quality:', error);
    return {
      backgroundClarity: 0,
      foreignElements: 0,
      sharpness: 0,
      resolution: 0,
      centering: 0,
      overall: 0
    };
  }
}

/**
 * Detect image angle/view using AI
 */
async function detectImageAngle(imageUrl: string, productType: string): Promise<string> {
  const prompt = `Analyze this ${productType} image and determine the viewing angle.
  
  Return one of these exact values:
  - "front" (front view)
  - "back" (back view)  
  - "side_left" (left side view)
  - "side_right" (right side view)
  - "top" (top/overhead view)
  - "sole" (bottom/sole view for shoes)
  - "close_up" (detail/texture close-up)
  - "three_quarter" (angled view)
  - "unknown" (cannot determine)
  
  Return only the angle name, nothing else.`;

  try {
    const result = await visionModel.generateContent([
      { text: prompt },
      { fileData: { mimeType: 'image/jpeg', fileUri: imageUrl } }
    ]);

    const response = await result.response;
    const angle = response.text().trim().toLowerCase();
    
    const validAngles = ['front', 'back', 'side_left', 'side_right', 'top', 'sole', 'close_up', 'three_quarter'];
    return validAngles.includes(angle) ? angle : 'unknown';
  } catch (error) {
    console.error('Error detecting image angle:', error);
    return 'unknown';
  }
}

/**
 * Enhanced image search with Google Custom Search API
 */
async function searchImagesOnline(query: string): Promise<Array<{
  url: string; 
  width: number; 
  height: number; 
  source: string;
}>> {
  try {
    const apiKey = process.env.GOOGLE_CUSTOM_SEARCH_API_KEY;
    const searchEngineId = process.env.GOOGLE_CUSTOM_SEARCH_ENGINE_ID;

    if (!apiKey || !searchEngineId) {
      console.warn('Google Custom Search API not configured');
      return [];
    }

    const searchUrl = `https://www.googleapis.com/customsearch/v1?key=${apiKey}&cx=${searchEngineId}&q=${encodeURIComponent(query)}&searchType=image&num=10&imgSize=large&imgType=photo&safe=active&imgColorType=color&imgDominantColor=white`;

    const response = await axios.get(searchUrl);

    if (!response.data.items) {
      return [];
    }

    return response.data.items.map((item: any) => ({
      url: item.link,
      width: parseInt(item.image?.width || '0'),
      height: parseInt(item.image?.height || '0'),
      source: item.displayLink || 'google_search'
    })).filter((img: any) => 
      img.width >= 800 && 
      img.height >= 800 && 
      img.url.match(/\.(jpg|jpeg|png|webp)$/i)
    );

  } catch (error) {
    console.error('Error searching for images:', error);
    return [];
  }
}
