NEXT_PUBLIC_APP_URL="https://rivvsneakers.shop"

# better-auth
BETTER_AUTH_SECRET=oe87VWyinBa24glMz0tVIYRA8XaQOye5
BETTER_AUTH_URL=http://localhost:3000 #Base URL of your app

# neon database
DATABASE_URL="postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require"


# uploadthing
UPLOADTHING_TOKEN='************************************************************************************************************************************************************************'


# google auth
GOOGLE_CLIENT_ID=254276333486-m4ralr16ol3j78nn8p3dk76abgou72cb.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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

# resend
RESEND_API_KEY=re_KvoNHC13_Nc3yZ2RozEHsfDZSxPpMiTDJ

# admin email for notifications
ADMIN_EMAIL=<EMAIL>

REPLICATE_API_TOKEN=****************************************