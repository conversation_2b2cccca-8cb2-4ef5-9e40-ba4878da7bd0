#!/usr/bin/env node

/**
 * View enhanced products and their status
 */

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function viewEnhancedProducts() {
  console.log('📊 Enhanced Products Overview');
  console.log('============================\n');

  // Get enhancement statistics
  const stats = await prisma.product.groupBy({
    by: ['enhancementStatus'],
    _count: true
  });

  console.log('📈 Enhancement Status Distribution:');
  stats.forEach(stat => {
    const status = stat.enhancementStatus || 'not_processed';
    console.log(`   ${status}: ${stat._count} products`);
  });

  // Get some sample enhanced products
  const enhancedProducts = await prisma.product.findMany({
    where: {
      enhancementStatus: 'completed'
    },
    take: 10,
    select: {
      id: true,
      name: true,
      brand: true,
      images: true,
      qualityScore: true,
      enhancementStatus: true,
      updatedAt: true
    },
    orderBy: {
      updatedAt: 'desc'
    }
  });

  console.log('\n🎨 Sample Enhanced Products:');
  enhancedProducts.forEach((product, index) => {
    console.log(`\n${index + 1}. ${product.name} (${product.brand})`);
    console.log(`   Status: ${product.enhancementStatus}`);
    console.log(`   Quality Score: ${product.qualityScore || 'N/A'}`);
    console.log(`   Images: ${product.images.length}`);
    console.log(`   Last Updated: ${product.updatedAt.toLocaleDateString()}`);
    
    if (product.images.length > 0) {
      console.log(`   First Image: ${product.images[0].substring(0, 60)}...`);
    }
  });

  // Check for products that might need re-enhancement
  const oldProducts = await prisma.product.count({
    where: {
      AND: [
        { enhancementStatus: 'completed' },
        { qualityScore: { lt: 70 } }
      ]
    }
  });

  if (oldProducts > 0) {
    console.log(`\n⚠️  Found ${oldProducts} products with quality scores below 70% that might benefit from re-enhancement.`);
  }

  await prisma.$disconnect();
}

viewEnhancedProducts().catch(console.error);
