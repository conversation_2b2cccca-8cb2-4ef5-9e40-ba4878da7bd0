import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth-utils';
import prisma from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    // Check authentication and admin role
    const user = await getCurrentUser();
    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 401 }
      );
    }

    // Get enhancement statistics
    const [
      totalProducts,
      completedProducts,
      processingProducts,
      failedProducts,
      pendingProducts,
      qualityStats
    ] = await Promise.all([
      // Total products with images
      prisma.product.count({
        where: {
          isActive: true,
          images: { isEmpty: false }
        }
      }),
      
      // Completed enhancements
      prisma.product.count({
        where: {
          isActive: true,
          images: { isEmpty: false },
          enhancementStatus: 'completed'
        }
      }),
      
      // Currently processing
      prisma.product.count({
        where: {
          isActive: true,
          images: { isEmpty: false },
          enhancementStatus: 'processing'
        }
      }),
      
      // Failed enhancements
      prisma.product.count({
        where: {
          isActive: true,
          images: { isEmpty: false },
          enhancementStatus: 'failed'
        }
      }),
      
      // Pending enhancements (null or not started)
      prisma.product.count({
        where: {
          isActive: true,
          images: { isEmpty: false },
          OR: [
            { enhancementStatus: null },
            { enhancementStatus: 'pending' }
          ]
        }
      }),
      
      // Average quality score
      prisma.product.aggregate({
        where: {
          isActive: true,
          enhancementStatus: 'completed',
          qualityScore: { not: null }
        },
        _avg: {
          qualityScore: true
        }
      })
    ]);

    // Calculate total processing time from enhancement logs
    const processingTimeStats = await prisma.imageEnhancementLog.aggregate({
      where: {
        status: 'completed',
        processingTime: { not: null }
      },
      _sum: {
        processingTime: true
      }
    });

    const stats = {
      total: totalProducts,
      completed: completedProducts,
      processing: processingProducts,
      failed: failedProducts,
      pending: pendingProducts,
      averageQualityScore: qualityStats._avg.qualityScore || 0,
      totalProcessingTime: processingTimeStats._sum.processingTime || 0
    };

    return NextResponse.json(stats);

  } catch (error) {
    console.error('Error fetching enhancement stats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch enhancement statistics' },
      { status: 500 }
    );
  }
}
