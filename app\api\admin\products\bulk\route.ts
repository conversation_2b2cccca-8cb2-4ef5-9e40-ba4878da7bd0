import { NextRequest, NextResponse } from "next/server";
import { ApiResponse } from "@/utils/types";
import { requireAdmin } from "@/lib/auth-utils";
import prisma from "@/lib/prisma";
import { getProductDescriptionFromImage } from '@/utils/replicate';


// POST /api/admin/products/bulk - Create multiple products
export async function POST(request: NextRequest) {
  try {
    // Check admin access
    await requireAdmin();

    const body = await request.json();
    const { products } = body;

    if (!products || !Array.isArray(products) || products.length === 0) {
      return NextResponse.json(
        { success: false, error: "Products array is required" },
        { status: 400 }
      );
    }

    // Validate each product
    const validationErrors: string[] = [];
    const validatedProducts: any[] = [];

    for (let i = 0; i < products.length; i++) {
      const product = products[i];
      const errors: string[] = [];

      // Required field validation
      if (!product.name?.trim()) {
        errors.push(`Product ${i + 1}: Name is required`);
      }
      if (!product.price || isNaN(parseFloat(product.price)) || parseFloat(product.price) <= 0) {
        errors.push(`Product ${i + 1}: Valid price is required`);
      }
      if (!product.brand?.trim()) {
        errors.push(`Product ${i + 1}: Brand is required`);
      }

      // Optional field validation
      if (product.discountedPrice && (isNaN(parseFloat(product.discountedPrice)) || parseFloat(product.discountedPrice) < 0)) {
        errors.push(`Product ${i + 1}: Invalid discounted price`);
      }
      if (product.stock && (isNaN(parseInt(product.stock)) || parseInt(product.stock) < 0)) {
        errors.push(`Product ${i + 1}: Invalid stock quantity`);
      }

      if (errors.length > 0) {
        validationErrors.push(...errors);
      } else {
        validatedProducts.push({
          name: product.name.trim(),
          description: product.description?.trim() || null,
          price: parseFloat(product.price),
          discountedPrice: product.discountedPrice ? parseFloat(product.discountedPrice) : null,
          brand: product.brand.trim(),
          categoryId: product.categoryId,
          images: Array.isArray(product.images) ? product.images : [],
          sizes: Array.isArray(product.sizes) ? product.sizes : [],
          stock: product.stock ? parseInt(product.stock) : 0,
          isActive: product.isActive !== undefined ? product.isActive : true,
        });
      }
    }

    if (validationErrors.length > 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: "Validation failed", 
          details: validationErrors 
        },
        { status: 400 }
      );
    }

    // Verify all categories exist
    const productCategoryIds = products.map(p => p.categoryId).filter(Boolean);
    const existingCategories: { id: string }[] = await prisma.category.findMany({
      where: { id: { in: productCategoryIds } },
      select: { id: true }
    });
    const existingCategoryIds = existingCategories.map(c => c.id);
    const missingCategories = productCategoryIds.filter(id => !existingCategoryIds.includes(id));

    if (missingCategories.length > 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: "Invalid categories", 
          details: [`Categories not found: ${missingCategories.join(", ")}`]
        },
        { status: 400 }
      );
    }

    // Create products in batches to avoid timeout
    const batchSize = 50;
    const createdProducts: any[] = [];
    const errors: string[] = [];

    for (let i = 0; i < validatedProducts.length; i += batchSize) {
      const batch = validatedProducts.slice(i, i + batchSize);
      
      try {
        const batchResults = await Promise.allSettled(
          batch.map(productData => 
            prisma.product.create({
              data: productData,
              include: {
                category: true,
              },
            })
          )
        );

        batchResults.forEach((result, index) => {
          if (result.status === 'fulfilled') {
            createdProducts.push(result.value);
          } else {
            errors.push(`Product ${i + index + 1}: ${result.reason.message || 'Failed to create'}`);
          }
        });
      } catch (error) {
        console.error(`Batch ${Math.floor(i / batchSize) + 1} failed:`, error);
        errors.push(`Batch ${Math.floor(i / batchSize) + 1} failed to process`);
      }
    }

    const response: ApiResponse<{
      created: any[];
      errors: string[];
      summary: {
        total: number;
        successful: number;
        failed: number;
      };
    }> = {
      success: createdProducts.length > 0,
      data: {
        created: createdProducts,
        errors,
        summary: {
          total: validatedProducts.length,
          successful: createdProducts.length,
          failed: errors.length,
        },
      },
      message: `Bulk upload completed: ${createdProducts.length} products created, ${errors.length} failed`,
    };

    return NextResponse.json(response, { status: createdProducts.length > 0 ? 201 : 400 });
  } catch (error) {
    console.error("Error in bulk product creation:", error);
    return NextResponse.json(
      { success: false, error: "Failed to create products" },
      { status: 500 }
    );
  }
}

// GET /api/admin/products/bulk/template - Get template for bulk upload
export async function GET(request: NextRequest) {
  try {
    // Check admin access
    await requireAdmin();

    // Get categories for template
    const categories = await prisma.category.findMany({
      select: { id: true, name: true },
      orderBy: { name: "asc" },
    });

    const template = {
      fields: [
        { name: "name", type: "string", required: true, description: "Product name" },
        { name: "description", type: "string", required: false, description: "Product description" },
        { name: "price", type: "number", required: true, description: "Product price in M" },
        { name: "discountedPrice", type: "number", required: false, description: "Discounted price (optional)" },
        { name: "brand", type: "string", required: true, description: "Product brand" },
        { name: "categoryId", type: "string", required: true, description: "Category ID" },
        { name: "images", type: "array", required: false, description: "Array of image URLs" },
        { name: "sizes", type: "array", required: false, description: "Available sizes" },
        { name: "stock", type: "number", required: false, description: "Stock quantity (default: 0)" },
        { name: "isActive", type: "boolean", required: false, description: "Product status (default: true)" },
      ],
      categories,
      example: {
        name: "Sample Product",
        description: "This is a sample product description",
        price: 299.99,
        discountedPrice: 249.99,
        brand: "Sample Brand",
        categoryId: categories[0]?.id || "category-id",
        images: ["https://example.com/image1.jpg", "https://example.com/image2.jpg"],
        sizes: ["3", "4", "5", "6", "7", "8", "9", "10"],
        stock: 100,
        isActive: true,
      },
    };

    const response: ApiResponse<typeof template> = {
      success: true,
      data: template,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching bulk upload template:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch template" },
      { status: 500 }
    );
  }
}
