#!/usr/bin/env ts-node

/**
 * Enhanced Product Image Enhancement Script
 * Applies the new AI-powered enhancement system to all existing products
 * 
 * Usage:
 * pnpm exec ts-node scripts/enhance-all-existing-products.ts
 * 
 * Options:
 * --dry-run: Preview what would be enhanced without making changes
 * --batch-size=10: Number of products to process concurrently
 * --confidence-threshold=80: Minimum confidence for auto-approval
 * --filter=pending: Only process products with specific status
 */

import prisma from '../lib/prisma';
import { enhanceProductImages } from '../utils/advanced-image-processor';
import { Command } from 'commander';

interface ProcessingStats {
  total: number;
  processed: number;
  autoApproved: number;
  manualReview: number;
  failed: number;
  skipped: number;
  startTime: Date;
}

interface ProcessingOptions {
  dryRun: boolean;
  batchSize: number;
  confidenceThreshold: number;
  filter: 'all' | 'pending' | 'failed' | 'no-images';
  autoApprove: boolean;
  verbose: boolean;
}

async function main() {
  const program = new Command();
  
  program
    .name('enhance-all-existing-products')
    .description('Apply AI-powered enhancement to existing product images')
    .option('--dry-run', 'Preview what would be enhanced without making changes', false)
    .option('--batch-size <number>', 'Number of products to process concurrently', '3')
    .option('--confidence-threshold <number>', 'Minimum confidence for auto-approval', '80')
    .option('--filter <type>', 'Filter products to process', 'all')
    .option('--auto-approve', 'Enable auto-approval for high-confidence results', true)
    .option('--verbose', 'Enable verbose logging', false)
    .parse();

  const options: ProcessingOptions = {
    dryRun: program.opts().dryRun,
    batchSize: parseInt(program.opts().batchSize),
    confidenceThreshold: parseInt(program.opts().confidenceThreshold),
    filter: program.opts().filter,
    autoApprove: program.opts().autoApprove,
    verbose: program.opts().verbose
  };

  console.log('🚀 Rivv AI Image Enhancement System');
  console.log('=====================================');
  console.log(`Mode: ${options.dryRun ? 'DRY RUN (Preview Only)' : 'LIVE PROCESSING'}`);
  console.log(`Batch Size: ${options.batchSize}`);
  console.log(`Confidence Threshold: ${options.confidenceThreshold}%`);
  console.log(`Auto-Approve: ${options.autoApprove ? 'Enabled' : 'Disabled'}`);
  console.log(`Filter: ${options.filter}`);
  console.log('');

  if (options.dryRun) {
    console.log('⚠️  DRY RUN MODE - No changes will be made to the database');
    console.log('');
  }

  // Get products to process
  const products = await getProductsToProcess(options.filter);
  
  if (products.length === 0) {
    console.log('✅ No products found matching the filter criteria.');
    return;
  }

  console.log(`📊 Found ${products.length} products to process`);
  
  if (options.dryRun) {
    console.log('\n📋 Products that would be processed:');
    products.forEach((product, index) => {
      console.log(`${index + 1}. ${product.name} (${product.brand}) - ${product.images.length} images`);
    });
    console.log('\n✅ Dry run complete. Use without --dry-run to process these products.');
    return;
  }

  // Confirm before processing
  console.log('\n⚠️  This will process all listed products. Continue? (y/N)');
  const readline = require('readline').createInterface({
    input: process.stdin,
    output: process.stdout
  });

  const answer = await new Promise<string>((resolve) => {
    readline.question('', resolve);
  });
  readline.close();

  if (answer.toLowerCase() !== 'y' && answer.toLowerCase() !== 'yes') {
    console.log('❌ Operation cancelled.');
    return;
  }

  // Initialize stats
  const stats: ProcessingStats = {
    total: products.length,
    processed: 0,
    autoApproved: 0,
    manualReview: 0,
    failed: 0,
    skipped: 0,
    startTime: new Date()
  };

  console.log('\n🔄 Starting enhancement process...\n');

  // Process products in batches
  for (let i = 0; i < products.length; i += options.batchSize) {
    const batch = products.slice(i, i + options.batchSize);
    
    console.log(`📦 Processing batch ${Math.floor(i / options.batchSize) + 1}/${Math.ceil(products.length / options.batchSize)} (${batch.length} products)`);
    
    const batchPromises = batch.map(product => processProduct(product, options, stats));
    await Promise.all(batchPromises);
    
    // Progress update
    const progress = ((i + batch.length) / products.length * 100).toFixed(1);
    console.log(`📈 Progress: ${progress}% (${stats.processed}/${stats.total} processed)\n`);
    
    // Small delay between batches to be respectful to APIs
    if (i + options.batchSize < products.length) {
      console.log('⏳ Waiting 3 seconds before next batch...\n');
      await new Promise(resolve => setTimeout(resolve, 3000));
    }
  }

  // Final report
  printFinalReport(stats);
}

async function getProductsToProcess(filter: string) {
  const baseWhere = {
    images: { isEmpty: false } // Only products with images
  };

  let whereClause;
  
  switch (filter) {
    case 'pending':
      whereClause = {
        ...baseWhere,
        OR: [
          { enhancementStatus: null },
          { enhancementStatus: 'pending' }
        ]
      };
      break;
    case 'failed':
      whereClause = {
        ...baseWhere,
        enhancementStatus: 'failed'
      };
      break;
    case 'no-images':
      whereClause = {
        images: { isEmpty: true }
      };
      break;
    case 'all':
    default:
      whereClause = {
        ...baseWhere,
        OR: [
          { enhancementStatus: null },
          { enhancementStatus: 'pending' },
          { enhancementStatus: 'failed' }
        ]
      };
      break;
  }

  return await prisma.product.findMany({
    where: whereClause,
    select: {
      id: true,
      name: true,
      brand: true,
      images: true,
      enhancementStatus: true,
      qualityScore: true
    },
    orderBy: { createdAt: 'desc' }
  });
}

async function processProduct(
  product: any, 
  options: ProcessingOptions, 
  stats: ProcessingStats
): Promise<void> {
  try {
    if (!product.images || product.images.length === 0) {
      console.log(`⏭️  Skipping ${product.name} - No images`);
      stats.skipped++;
      return;
    }

    const originalImageUrl = product.images[0];
    console.log(`🔄 Processing: ${product.name} (${product.brand})`);
    
    if (options.verbose) {
      console.log(`   Original image: ${originalImageUrl}`);
    }

    // Mark as processing
    await prisma.product.update({
      where: { id: product.id },
      data: { enhancementStatus: 'processing' }
    });

    // Run AI enhancement
    const enhancementResult = await enhanceProductImages(originalImageUrl, product.id);
    
    // Get auto-approval decision
    const autoApprovalDecision = enhancementResult.auto_approval_decision;
    const shouldAutoApprove = options.autoApprove && 
                             autoApprovalDecision?.shouldAutoApprove &&
                             autoApprovalDecision.riskLevel === 'low' &&
                             enhancementResult.confidence_score >= options.confidenceThreshold;

    if (shouldAutoApprove && enhancementResult.images.length > 0) {
      // Auto-approve and update
      const imageUrls = enhancementResult.images.map(img => img.url);
      
      await prisma.product.update({
        where: { id: product.id },
        data: {
          images: imageUrls,
          enhancementStatus: 'completed',
          qualityScore: enhancementResult.confidence_score
        }
      });

      console.log(`✅ Auto-approved: ${product.name} - ${enhancementResult.images.length} images (${enhancementResult.confidence_score}% confidence)`);
      stats.autoApproved++;
      
    } else if (enhancementResult.images.length > 0) {
      // Mark for manual review
      await prisma.product.update({
        where: { id: product.id },
        data: {
          enhancementStatus: 'review_required',
          qualityScore: enhancementResult.confidence_score
        }
      });

      console.log(`⚠️  Manual review: ${product.name} - ${enhancementResult.images.length} images (${enhancementResult.confidence_score}% confidence, ${autoApprovalDecision?.riskLevel} risk)`);
      stats.manualReview++;
      
    } else {
      // No suitable images found
      await prisma.product.update({
        where: { id: product.id },
        data: { enhancementStatus: 'failed' }
      });

      console.log(`❌ Failed: ${product.name} - No suitable enhanced images found`);
      stats.failed++;
    }

    stats.processed++;

  } catch (error) {
    console.error(`❌ Error processing ${product.name}:`, error);
    
    // Mark as failed
    await prisma.product.update({
      where: { id: product.id },
      data: { enhancementStatus: 'failed' }
    });

    stats.failed++;
    stats.processed++;
  }
}

function printFinalReport(stats: ProcessingStats) {
  const endTime = new Date();
  const duration = Math.round((endTime.getTime() - stats.startTime.getTime()) / 1000);
  const minutes = Math.floor(duration / 60);
  const seconds = duration % 60;

  console.log('\n🎉 Enhancement Process Complete!');
  console.log('================================');
  console.log(`⏱️  Total Time: ${minutes}m ${seconds}s`);
  console.log(`📊 Total Products: ${stats.total}`);
  console.log(`✅ Auto-Approved: ${stats.autoApproved}`);
  console.log(`⚠️  Manual Review: ${stats.manualReview}`);
  console.log(`❌ Failed: ${stats.failed}`);
  console.log(`⏭️  Skipped: ${stats.skipped}`);
  console.log(`📈 Success Rate: ${((stats.autoApproved + stats.manualReview) / stats.total * 100).toFixed(1)}%`);
  
  if (stats.manualReview > 0) {
    console.log('\n📋 Next Steps:');
    console.log('• Review products marked "review_required" in the admin dashboard');
    console.log('• Check confidence analysis for manual review items');
    console.log('• Approve or reject enhanced images as needed');
  }
  
  if (stats.failed > 0) {
    console.log('\n🔧 Failed Products:');
    console.log('• Check error logs for specific failure reasons');
    console.log('• Verify API credentials and quotas');
    console.log('• Consider re-running with --filter=failed');
  }
}

// Run the script
main().catch(console.error).finally(() => process.exit(0));
