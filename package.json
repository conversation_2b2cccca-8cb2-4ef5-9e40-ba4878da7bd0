{"name": "rivv_ecommerce", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "prisma generate && next build", "start": "next start", "lint": "next lint", "enhance-products": "node scripts/enhance-existing-products.js", "enhance-products-ts": "tsx scripts/enhance-existing-products.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "db:migrate": "prisma migrate dev", "db:generate": "prisma generate", "db:studio": "prisma studio"}, "dependencies": {"@google/generative-ai": "^0.24.1", "@hookform/resolvers": "^5.1.1", "@prisma/client": "^6.11.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@types/date-fns": "^2.6.3", "@types/qrcode": "^1.5.5", "@uploadthing/react": "^7.3.2", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "axios": "^1.11.0", "better-auth": "^1.2.12", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^17.2.1", "framer-motion": "^12.23.9", "html5-qrcode": "^2.3.8", "lucide-react": "^0.525.0", "next": "15.3.4", "qrcode": "^1.5.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.59.0", "react-hot-toast": "^2.5.2", "resend": "^4.6.0", "sharp": "^0.34.3", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "uploadthing": "^7.7.3", "uuid": "^11.1.0", "zod": "^3.25.67"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^20.19.2", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "prisma": "^6.11.0", "tailwindcss": "^4", "ts-node": "^10.9.2", "tsx": "^4.20.3", "tw-animate-css": "^1.3.4", "typescript": "^5.8.3"}, "packageManager": "pnpm@10.5.2+sha512.da9dc28cd3ff40d0592188235ab25d3202add8a207afbedc682220e4a0029ffbff4562102b9e6e46b4e3f9e8bd53e6d05de48544b0c57d4b0179e22c76d1199b", "pnpm": {"onlyBuiltDependencies": ["@prisma/client", "@prisma/engines", "@tailwindcss/oxide", "@vercel/speed-insights", "msgpackr-extract", "prisma", "sharp"]}}