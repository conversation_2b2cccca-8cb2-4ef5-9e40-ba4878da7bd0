'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Brain,
  CheckCircle,
  AlertTriangle,
  XCircle,
  TrendingUp,
  Eye,
  Shield,
  Target,
  Award,
  BarChart3
} from 'lucide-react';

interface ConfidenceFactors {
  productIdentification: number;
  imageQuality: number;
  sourceReliability: number;
  angleCompleteness: number;
  brandRecognition: number;
  consistencyScore: number;
}

interface AutoApprovalDecision {
  shouldAutoApprove: boolean;
  confidence: number;
  factors: ConfidenceFactors;
  reasoning: string[];
  riskLevel: 'low' | 'medium' | 'high';
  recommendedAction: 'auto_approve' | 'manual_review' | 'reject';
}

interface ConfidenceScoreDashboardProps {
  decision: AutoApprovalDecision;
  className?: string;
}

export default function ConfidenceScoreDashboard({ decision, className = '' }: ConfidenceScoreDashboardProps) {
  const { confidence, factors, reasoning, riskLevel, recommendedAction, shouldAutoApprove } = decision;

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreVariant = (score: number) => {
    if (score >= 80) return 'default';
    if (score >= 60) return 'secondary';
    return 'destructive';
  };

  const getRiskBadge = (risk: string) => {
    switch (risk) {
      case 'low':
        return <Badge variant="default" className="bg-green-500"><Shield className="w-3 h-3 mr-1" />Low Risk</Badge>;
      case 'medium':
        return <Badge variant="secondary"><AlertTriangle className="w-3 h-3 mr-1" />Medium Risk</Badge>;
      case 'high':
        return <Badge variant="destructive"><XCircle className="w-3 h-3 mr-1" />High Risk</Badge>;
      default:
        return <Badge variant="outline">{risk}</Badge>;
    }
  };

  const getActionBadge = (action: string) => {
    switch (action) {
      case 'auto_approve':
        return <Badge variant="default" className="bg-green-500"><CheckCircle className="w-3 h-3 mr-1" />Auto Approve</Badge>;
      case 'manual_review':
        return <Badge variant="secondary"><Eye className="w-3 h-3 mr-1" />Manual Review</Badge>;
      case 'reject':
        return <Badge variant="destructive"><XCircle className="w-3 h-3 mr-1" />Reject</Badge>;
      default:
        return <Badge variant="outline">{action}</Badge>;
    }
  };

  const factorDetails = [
    {
      name: 'Product Identification',
      score: factors.productIdentification,
      icon: Target,
      description: 'How well the AI identified the product details'
    },
    {
      name: 'Image Quality',
      score: factors.imageQuality,
      icon: Eye,
      description: 'Overall quality of enhanced images'
    },
    {
      name: 'Source Reliability',
      score: factors.sourceReliability,
      icon: Shield,
      description: 'Trustworthiness of image sources'
    },
    {
      name: 'Angle Completeness',
      score: factors.angleCompleteness,
      icon: BarChart3,
      description: 'Coverage of different viewing angles'
    },
    {
      name: 'Brand Recognition',
      score: factors.brandRecognition,
      icon: Award,
      description: 'Recognition level of the brand'
    },
    {
      name: 'Consistency',
      score: factors.consistencyScore,
      icon: TrendingUp,
      description: 'Consistency across all enhanced images'
    }
  ];

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Overall Confidence Score */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="w-5 h-5 text-purple-500" />
            AI Confidence Analysis
          </CardTitle>
          <CardDescription>
            Advanced scoring system for enhancement quality assessment
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Main Score */}
          <div className="text-center">
            <div className={`text-6xl font-bold ${getScoreColor(confidence)}`}>
              {confidence}%
            </div>
            <p className="text-muted-foreground mt-2">Overall Confidence Score</p>
          </div>

          {/* Progress Bar */}
          <div className="space-y-2">
            <Progress value={confidence} className="h-3" />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>0%</span>
              <span>50%</span>
              <span>100%</span>
            </div>
          </div>

          {/* Decision Summary */}
          <div className="flex items-center justify-center gap-4">
            {getRiskBadge(riskLevel)}
            {getActionBadge(recommendedAction)}
          </div>

          {/* Auto-Approval Status */}
          {shouldAutoApprove ? (
            <Alert className="border-green-200 bg-green-50">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <AlertDescription className="text-green-800">
                ✅ Approved for automatic processing - High confidence with low risk
              </AlertDescription>
            </Alert>
          ) : (
            <Alert className="border-yellow-200 bg-yellow-50">
              <AlertTriangle className="h-4 w-4 text-yellow-600" />
              <AlertDescription className="text-yellow-800">
                ⚠️ Manual review required - See detailed analysis below
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Detailed Factor Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle>Confidence Factor Breakdown</CardTitle>
          <CardDescription>
            Detailed analysis of each scoring component
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {factorDetails.map((factor, index) => {
              const Icon = factor.icon;
              return (
                <div key={index} className="p-4 border rounded-lg space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Icon className="w-4 h-4 text-muted-foreground" />
                      <span className="font-medium text-sm">{factor.name}</span>
                    </div>
                    <Badge variant={getScoreVariant(factor.score)} className="text-xs">
                      {factor.score}%
                    </Badge>
                  </div>
                  
                  <Progress value={factor.score} className="h-2" />
                  
                  <p className="text-xs text-muted-foreground">
                    {factor.description}
                  </p>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* AI Reasoning */}
      <Card>
        <CardHeader>
          <CardTitle>AI Decision Reasoning</CardTitle>
          <CardDescription>
            Detailed explanation of the confidence assessment
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {reasoning.map((reason, index) => (
              <div key={index} className="flex items-start gap-2 text-sm">
                <div className="w-2 h-2 rounded-full bg-muted-foreground mt-2 flex-shrink-0" />
                <span className={reason.includes('✅') ? 'text-green-700' : reason.includes('⚠️') ? 'text-yellow-700' : 'text-muted-foreground'}>
                  {reason}
                </span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle>Recommendations</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {recommendedAction === 'auto_approve' && (
              <Alert className="border-green-200 bg-green-50">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <AlertDescription className="text-green-800">
                  <strong>Recommended Action:</strong> Proceed with automatic approval. The enhancement meets all quality standards with high confidence.
                </AlertDescription>
              </Alert>
            )}
            
            {recommendedAction === 'manual_review' && (
              <Alert className="border-yellow-200 bg-yellow-50">
                <AlertTriangle className="h-4 w-4 text-yellow-600" />
                <AlertDescription className="text-yellow-800">
                  <strong>Recommended Action:</strong> Manual review required. Please verify the enhanced images before approval.
                  {factors.imageQuality < 70 && <div className="mt-1">• Consider checking image quality</div>}
                  {factors.productIdentification < 60 && <div className="mt-1">• Verify product identification accuracy</div>}
                  {factors.sourceReliability < 70 && <div className="mt-1">• Review image source reliability</div>}
                </AlertDescription>
              </Alert>
            )}
            
            {recommendedAction === 'reject' && (
              <Alert variant="destructive">
                <XCircle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Recommended Action:</strong> Enhancement not recommended. Consider using the original image or trying again with different parameters.
                </AlertDescription>
              </Alert>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
