import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/auth-utils";
import { enhanceProductImages } from "@/utils/advanced-image-processor";

/**
 * POST /api/admin/products/preview-enhancement
 * Preview AI Enhancement without applying to product
 * Shows what the enhancement would look like before product creation
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication and admin role
    const user = await getCurrentUser();
    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { imageUrl } = body;

    // Validate required fields
    if (!imageUrl) {
      return NextResponse.json(
        { error: 'Missing required field: imageUrl' },
        { status: 400 }
      );
    }

    console.log(`🔍 Generating enhancement preview for: ${imageUrl}`);

    // Run enhancement preview (without saving to product)
    const enhancementResult = await enhanceProductImages(imageUrl);

    // Return preview data
    const preview = {
      original_image: imageUrl,
      enhancement_preview: {
        product_identification: {
          brand: enhancementResult.product_name.split(' ')[0] || 'Unknown',
          product_type: 'Detected from image',
          confidence: enhancementResult.confidence_score
        },
        enhanced_images: enhancementResult.images.map(img => ({
          angle: img.angle,
          url: img.url,
          quality_score: img.qualityScore,
          source: img.source
        })),
        image_source: enhancementResult.image_source,
        total_images_found: enhancementResult.images.length,
        processing_notes: enhancementResult.processing_notes.slice(-5), // Last 5 notes
        recommendation: getEnhancementRecommendation(enhancementResult)
      }
    };

    return NextResponse.json({
      success: true,
      data: preview,
      message: `Enhancement preview generated. Found ${enhancementResult.images.length} enhanced images.`
    });

  } catch (error) {
    console.error('Error generating enhancement preview:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Preview generation failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * Generate recommendation based on enhancement results
 */
function getEnhancementRecommendation(result: any): {
  action: 'auto_approve' | 'manual_review' | 'use_original';
  reason: string;
  confidence: 'high' | 'medium' | 'low';
} {
  const { confidence_score, images, image_source } = result;
  
  if (confidence_score >= 85 && images.length >= 3 && image_source === 'web_fetch') {
    return {
      action: 'auto_approve',
      reason: 'High confidence with multiple high-quality images found online',
      confidence: 'high'
    };
  }
  
  if (confidence_score >= 70 && images.length >= 1 && image_source === 'web_fetch') {
    return {
      action: 'manual_review',
      reason: 'Good quality images found but manual review recommended',
      confidence: 'medium'
    };
  }
  
  if (confidence_score >= 50 && image_source === 'enhanced_original') {
    return {
      action: 'manual_review',
      reason: 'Original image enhanced - review quality before approval',
      confidence: 'medium'
    };
  }
  
  return {
    action: 'use_original',
    reason: 'Low confidence or poor quality - consider using original image',
    confidence: 'low'
  };
}
