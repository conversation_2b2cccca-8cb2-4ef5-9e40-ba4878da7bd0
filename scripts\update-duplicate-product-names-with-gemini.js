"use strict";
var __makeTemplateObject = (this && this.__makeTemplateObject) || function (cooked, raw) {
    if (Object.defineProperty) { Object.defineProperty(cooked, "raw", { value: raw }); } else { cooked.raw = raw; }
    return cooked;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
// scripts/update-duplicate-product-names-with-gemini.ts
var client_1 = require("@prisma/client");
var node_fetch_1 = require("node-fetch");
var prisma = new client_1.PrismaClient();
function main() {
    return __awaiter(this, void 0, void 0, function () {
        var duplicates, duplicateNames, _i, duplicateNames_1, name_1, productsToUpdate, _a, productsToUpdate_1, product, imageUrl, res, errorBody, data, geminiResult, newName, err_1;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    console.log('🔍 Finding duplicate product names...');
                    return [4 /*yield*/, prisma.$queryRaw(templateObject_1 || (templateObject_1 = __makeTemplateObject(["\n    SELECT name\n    FROM \"Product\"\n    GROUP BY name\n    HAVING COUNT(*) > 1\n  "], ["\n    SELECT name\n    FROM \"Product\"\n    GROUP BY name\n    HAVING COUNT(*) > 1\n  "])))];
                case 1:
                    duplicates = _b.sent();
                    if (duplicates.length === 0) {
                        console.log('✅ No duplicate product names found. Exiting.');
                        return [2 /*return*/];
                    }
                    duplicateNames = duplicates.map(function (d) { return d.name; });
                    console.log("\uD83D\uDD25 Found ".concat(duplicateNames.length, " duplicate product name(s):"), duplicateNames);
                    _i = 0, duplicateNames_1 = duplicateNames;
                    _b.label = 2;
                case 2:
                    if (!(_i < duplicateNames_1.length)) return [3 /*break*/, 18];
                    name_1 = duplicateNames_1[_i];
                    return [4 /*yield*/, prisma.product.findMany({
                            where: { name: name_1 },
                        })];
                case 3:
                    productsToUpdate = _b.sent();
                    console.log("\n--- Processing ".concat(productsToUpdate.length, " products named \"").concat(name_1, "\" ---"));
                    _a = 0, productsToUpdate_1 = productsToUpdate;
                    _b.label = 4;
                case 4:
                    if (!(_a < productsToUpdate_1.length)) return [3 /*break*/, 17];
                    product = productsToUpdate_1[_a];
                    imageUrl = product.images[0];
                    if (!imageUrl) {
                        console.log("\u23ED\uFE0F  Skipping product ".concat(product.id, " (\"").concat(product.name, "\") - no image found."));
                        return [3 /*break*/, 16];
                    }
                    console.log("\uD83E\uDD16 Analyzing image for product ".concat(product.id, "..."));
                    _b.label = 5;
                case 5:
                    _b.trys.push([5, 15, , 16]);
                    return [4 /*yield*/, (0, node_fetch_1.default)('http://localhost:3000/api/analyze-sneaker', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ imageUrl: imageUrl }),
                        })];
                case 6:
                    res = _b.sent();
                    if (!!res.ok) return [3 /*break*/, 8];
                    return [4 /*yield*/, res.text()];
                case 7:
                    errorBody = _b.sent();
                    console.error("\u274C API Error for product ".concat(product.id, ": ").concat(res.status, " ").concat(res.statusText), errorBody);
                    return [3 /*break*/, 16];
                case 8: return [4 /*yield*/, res.json()];
                case 9:
                    data = _b.sent();
                    if (!(data && data.description)) return [3 /*break*/, 13];
                    geminiResult = void 0;
                    try {
                        geminiResult = JSON.parse(data.description);
                    }
                    catch (_c) {
                        console.log("\u26A0\uFE0F  Could not parse Gemini response for product ".concat(product.id, ", skipping."));
                        return [3 /*break*/, 16];
                    }
                    newName = geminiResult.name;
                    if (!(newName && newName !== product.name)) return [3 /*break*/, 11];
                    return [4 /*yield*/, prisma.product.update({
                            where: { id: product.id },
                            data: { name: newName },
                        })];
                case 10:
                    _b.sent();
                    console.log("\u2705 Updated product ".concat(product.id, ": \"").concat(product.name, "\" \u2192 \"").concat(newName, "\""));
                    return [3 /*break*/, 12];
                case 11:
                    console.log("\uD83E\uDD14 Gemini did not suggest a new name for product ".concat(product.id, ", skipping."));
                    _b.label = 12;
                case 12: return [3 /*break*/, 14];
                case 13:
                    console.log("\uD83E\uDD37 No description from Gemini for product ".concat(product.id, ", skipping."));
                    _b.label = 14;
                case 14: return [3 /*break*/, 16];
                case 15:
                    err_1 = _b.sent();
                    console.error("\uD83D\uDCA5 Critical error analyzing product ".concat(product.id, ":"), err_1);
                    return [3 /*break*/, 16];
                case 16:
                    _a++;
                    return [3 /*break*/, 4];
                case 17:
                    _i++;
                    return [3 /*break*/, 2];
                case 18: return [2 /*return*/];
            }
        });
    });
}
main()
    .catch(function (e) {
    console.error('❌ Script failed with an error:', e);
    process.exit(1);
})
    .finally(function () { return __awaiter(void 0, void 0, void 0, function () {
    return __generator(this, function (_a) {
        switch (_a.label) {
            case 0: return [4 /*yield*/, prisma.$disconnect()];
            case 1:
                _a.sent();
                console.log('\n🏁 Script finished.');
                return [2 /*return*/];
        }
    });
}); });
var templateObject_1;
