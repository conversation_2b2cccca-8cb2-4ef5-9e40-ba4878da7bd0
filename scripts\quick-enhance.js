#!/usr/bin/env node

/**
 * Quick Enhancement Script for Rivv Products
 * Simple script to enhance a few products at a time
 * 
 * Usage:
 * node scripts/quick-enhance.js
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function quickEnhance() {
  console.log('🚀 Rivv Quick Image Enhancement');
  console.log('===============================\n');

  // Get first 5 products that need enhancement
  const products = await prisma.product.findMany({
    where: {
      AND: [
        { 
          images: { 
            not: { 
              equals: [] 
            } 
          } 
        },
        { 
          OR: [
            { enhancementStatus: null },
            { enhancementStatus: 'pending' },
            { enhancementStatus: 'failed' }
          ]
        }
      ]
    },
    take: 5,
    select: {
      id: true,
      name: true,
      brand: true,
      images: true,
      enhancementStatus: true
    }
  });

  if (products.length === 0) {
    console.log('✅ No products found that need enhancement!');
    console.log('   All your products are already enhanced or have no images.');
    await prisma.$disconnect();
    return;
  }

  console.log(`📦 Found ${products.length} products to enhance:\n`);
  
  products.forEach((product, index) => {
    console.log(`${index + 1}. ${product.name} (${product.brand})`);
    console.log(`   Images: ${product.images.length}`);
    console.log(`   Status: ${product.enhancementStatus || 'not_processed'}`);
    console.log('');
  });

  console.log('🔄 Starting enhancement...\n');

  let processed = 0;
  let autoApproved = 0;
  let manualReview = 0;
  let failed = 0;

  for (const product of products) {
    try {
      console.log(`🔄 Processing: ${product.name}`);
      
      // Mark as processing
      await prisma.product.update({
        where: { id: product.id },
        data: { enhancementStatus: 'processing' }
      });

      // Call the enhancement API
      const response = await fetch('http://localhost:3000/api/admin/products/enhance-images', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          originalImageUrl: product.images[0],
          productId: product.id,
          autoApprove: true,
          confidenceThreshold: 80
        }),
      });

      if (!response.ok) {
        throw new Error(`API call failed: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.success) {
        if (result.data.auto_approved) {
          console.log(`✅ Auto-approved: ${result.data.images.length} images (${result.data.confidence_score}% confidence)`);
          autoApproved++;
        } else {
          console.log(`⚠️  Manual review needed (${result.data.confidence_score}% confidence)`);
          manualReview++;
        }
      } else {
        throw new Error(result.error || 'Enhancement failed');
      }
      
      processed++;
      console.log('');
      
    } catch (error) {
      console.error(`❌ Error: ${error.message}`);
      
      // Mark as failed
      await prisma.product.update({
        where: { id: product.id },
        data: { enhancementStatus: 'failed' }
      });
      
      failed++;
      processed++;
      console.log('');
    }
  }

  // Summary
  console.log('🎉 Quick Enhancement Complete!');
  console.log('==============================');
  console.log(`✅ Auto-approved: ${autoApproved}`);
  console.log(`⚠️  Manual review: ${manualReview}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📊 Total processed: ${processed}`);
  
  if (manualReview > 0) {
    console.log('\n📋 Next: Check admin dashboard for manual review items');
    console.log('   Go to: http://localhost:3000/admin/image-enhancement');
  }

  if (failed > 0) {
    console.log('\n🔧 Failed items may need:');
    console.log('   • Better product images');
    console.log('   • API quota check');
    console.log('   • Network connectivity check');
  }

  await prisma.$disconnect();
}

// Check if Next.js server is running first
async function checkServer() {
  try {
    const response = await fetch('http://localhost:3000/api/admin/products/enhance-images', {
      method: 'GET'
    });
    return response.ok;
  } catch (error) {
    return false;
  }
}

async function main() {
  console.log('🔍 Checking if Next.js server is running...');
  
  const serverRunning = await checkServer();
  
  if (!serverRunning) {
    console.log('❌ Next.js server is not running or enhancement API is not accessible.');
    console.log('\n🚀 Please start your server first:');
    console.log('   pnpm dev');
    console.log('\nThen run this script again:');
    console.log('   node scripts/quick-enhance.js');
    process.exit(1);
  }
  
  console.log('✅ Server is running, proceeding with enhancement...\n');
  await quickEnhance();
}

main().catch(console.error);
