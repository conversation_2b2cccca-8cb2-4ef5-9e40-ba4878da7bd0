import { NextRequest, NextResponse } from "next/server";
import { requireAdmin } from "@/lib/auth-utils";
import { getProductDescriptionFromImage } from '@/utils/replicate';

// POST /api/admin/products/bulk/metadata - Extract product metadata from images (AI)
export async function POST(request: NextRequest) {
  try {
    await requireAdmin();
    const body = await request.json();
    const { images } = body;
    if (!images || !Array.isArray(images) || images.length === 0) {
      return NextResponse.json({ success: false, error: 'Images array is required' }, { status: 400 });
    }
    const results = await Promise.all(
      images.map(async (imageUrl: string) => {
        try {
          const description = await getProductDescriptionFromImage(imageUrl);
          return { imageUrl, description };
        } catch (err) {
          return { imageUrl, description: null, error: (err as Error).message };
        }
      })
    );
    return NextResponse.json({ success: true, data: results });
  } catch (error) {
    return NextResponse.json({ success: false, error: 'Failed to extract metadata' }, { status: 500 });
  }
} 