'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Sparkles, 
  Play, 
  Pause, 
  RotateCcw,
  CheckCircle, 
  AlertCircle, 
  Clock,
  Filter,
  Settings
} from 'lucide-react';

interface Product {
  id: string;
  name: string;
  brand: string;
  images: string[];
  enhancementStatus?: string;
  qualityScore?: number;
}

interface BatchResult {
  productId: string;
  productName: string;
  status: 'completed' | 'review_required' | 'failed' | 'skipped';
  imagesGenerated?: number;
  confidenceScore?: number;
  autoApproved?: boolean;
  reason?: string;
  error?: string;
}

interface BatchImageEnhancerProps {
  products: Product[];
  onBatchComplete?: (results: BatchResult[]) => void;
}

export default function BatchImageEnhancer({ products, onBatchComplete }: BatchImageEnhancerProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);
  const [batchResults, setBatchResults] = useState<BatchResult[]>([]);
  const [progress, setProgress] = useState(0);
  const [currentProduct, setCurrentProduct] = useState<string>('');
  const [settings, setSettings] = useState({
    autoApprove: true,
    confidenceThreshold: 80,
    maxConcurrent: 3,
    processAll: false
  });
  const [filter, setFilter] = useState({
    status: 'all',
    hasImages: true,
    notEnhanced: true
  });

  // Filter products based on criteria
  const filteredProducts = products.filter(product => {
    if (filter.hasImages && (!product.images || product.images.length === 0)) return false;
    if (filter.notEnhanced && product.enhancementStatus === 'completed') return false;
    if (filter.status !== 'all' && product.enhancementStatus !== filter.status) return false;
    return true;
  });

  // Select/deselect all products
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedProducts(filteredProducts.map(p => p.id));
    } else {
      setSelectedProducts([]);
    }
  };

  // Toggle individual product selection
  const handleProductSelect = (productId: string, checked: boolean) => {
    if (checked) {
      setSelectedProducts(prev => [...prev, productId]);
    } else {
      setSelectedProducts(prev => prev.filter(id => id !== productId));
    }
  };

  // Start batch processing
  const handleStartBatch = async () => {
    if (selectedProducts.length === 0 && !settings.processAll) {
      alert('Please select products to enhance or enable "Process All"');
      return;
    }

    setIsProcessing(true);
    setBatchResults([]);
    setProgress(0);
    setCurrentProduct('');

    try {
      const response = await fetch('/api/admin/products/batch-enhance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          productIds: settings.processAll ? [] : selectedProducts,
          processAll: settings.processAll,
          confidenceThreshold: settings.confidenceThreshold,
          autoApprove: settings.autoApprove,
          maxConcurrent: settings.maxConcurrent
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Batch processing failed');
      }

      setBatchResults(data.data.results);
      setProgress(100);
      onBatchComplete?.(data.data.results);

    } catch (error) {
      console.error('Batch processing error:', error);
      alert(error instanceof Error ? error.message : 'Batch processing failed');
    } finally {
      setIsProcessing(false);
      setCurrentProduct('');
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'review_required':
        return <AlertCircle className="w-4 h-4 text-yellow-500" />;
      case 'failed':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      case 'processing':
        return <Clock className="w-4 h-4 text-blue-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge variant="default" className="bg-green-500">Completed</Badge>;
      case 'review_required':
        return <Badge variant="outline">Review Required</Badge>;
      case 'failed':
        return <Badge variant="destructive">Failed</Badge>;
      case 'skipped':
        return <Badge variant="secondary">Skipped</Badge>;
      default:
        return <Badge variant="outline">Pending</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      {/* Settings Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="w-5 h-5" />
            Batch Enhancement Settings
          </CardTitle>
          <CardDescription>
            Configure batch processing options
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="confidence">Confidence Threshold (%)</Label>
              <Input
                id="confidence"
                type="number"
                min="0"
                max="100"
                value={settings.confidenceThreshold}
                onChange={(e) => setSettings(prev => ({ 
                  ...prev, 
                  confidenceThreshold: parseInt(e.target.value) || 80 
                }))}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="concurrent">Max Concurrent</Label>
              <Input
                id="concurrent"
                type="number"
                min="1"
                max="10"
                value={settings.maxConcurrent}
                onChange={(e) => setSettings(prev => ({ 
                  ...prev, 
                  maxConcurrent: parseInt(e.target.value) || 3 
                }))}
              />
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Checkbox
              id="autoApprove"
              checked={settings.autoApprove}
              onCheckedChange={(checked) => setSettings(prev => ({ 
                ...prev, 
                autoApprove: checked as boolean 
              }))}
            />
            <Label htmlFor="autoApprove">Auto-approve high confidence results</Label>
          </div>
          
          <div className="flex items-center space-x-2">
            <Checkbox
              id="processAll"
              checked={settings.processAll}
              onCheckedChange={(checked) => setSettings(prev => ({ 
                ...prev, 
                processAll: checked as boolean 
              }))}
            />
            <Label htmlFor="processAll">Process all eligible products (ignore selection)</Label>
          </div>
        </CardContent>
      </Card>

      {/* Product Selection Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="w-5 h-5" />
            Product Selection ({filteredProducts.length} products)
          </CardTitle>
          <CardDescription>
            Select products for batch enhancement
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Filters */}
          <div className="flex gap-4 items-center">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="selectAll"
                checked={selectedProducts.length === filteredProducts.length && filteredProducts.length > 0}
                onCheckedChange={handleSelectAll}
              />
              <Label htmlFor="selectAll">Select All</Label>
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox
                id="hasImages"
                checked={filter.hasImages}
                onCheckedChange={(checked) => setFilter(prev => ({ 
                  ...prev, 
                  hasImages: checked as boolean 
                }))}
              />
              <Label htmlFor="hasImages">Has Images</Label>
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox
                id="notEnhanced"
                checked={filter.notEnhanced}
                onCheckedChange={(checked) => setFilter(prev => ({ 
                  ...prev, 
                  notEnhanced: checked as boolean 
                }))}
              />
              <Label htmlFor="notEnhanced">Not Enhanced</Label>
            </div>
          </div>

          {/* Product List */}
          <div className="max-h-60 overflow-y-auto space-y-2">
            {filteredProducts.map(product => (
              <div key={product.id} className="flex items-center justify-between p-2 border rounded">
                <div className="flex items-center space-x-3">
                  <Checkbox
                    checked={selectedProducts.includes(product.id)}
                    onCheckedChange={(checked) => handleProductSelect(product.id, checked as boolean)}
                  />
                  <div className="flex items-center gap-2">
                    {product.images && product.images.length > 0 && (
                      <img
                        src={product.images[0]}
                        alt={product.name}
                        className="w-8 h-8 object-cover rounded"
                      />
                    )}
                    <div>
                      <p className="font-medium text-sm">{product.name}</p>
                      <p className="text-xs text-muted-foreground">{product.brand}</p>
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {getStatusIcon(product.enhancementStatus || 'pending')}
                  {product.qualityScore && (
                    <Badge variant="outline" className="text-xs">
                      {product.qualityScore}%
                    </Badge>
                  )}
                </div>
              </div>
            ))}
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2">
            <Button
              onClick={handleStartBatch}
              disabled={isProcessing || (selectedProducts.length === 0 && !settings.processAll)}
              className="flex-1"
            >
              {isProcessing ? (
                <>
                  <Pause className="w-4 h-4 mr-2" />
                  Processing...
                </>
              ) : (
                <>
                  <Play className="w-4 h-4 mr-2" />
                  Start Batch Enhancement
                </>
              )}
            </Button>
            
            <Button
              variant="outline"
              onClick={() => {
                setBatchResults([]);
                setProgress(0);
                setCurrentProduct('');
              }}
              disabled={isProcessing}
            >
              <RotateCcw className="w-4 h-4 mr-2" />
              Reset
            </Button>
          </div>

          {/* Progress */}
          {isProcessing && (
            <div className="space-y-2">
              <Progress value={progress} className="w-full" />
              {currentProduct && (
                <p className="text-sm text-muted-foreground">
                  Processing: {currentProduct}
                </p>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Results Card */}
      {batchResults.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Sparkles className="w-5 h-5" />
              Batch Results ({batchResults.length} products)
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 max-h-60 overflow-y-auto">
              {batchResults.map((result, index) => (
                <div key={index} className="flex items-center justify-between p-2 border rounded">
                  <div>
                    <p className="font-medium text-sm">{result.productName}</p>
                    {result.reason && (
                      <p className="text-xs text-muted-foreground">{result.reason}</p>
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    {getStatusBadge(result.status)}
                    {result.confidenceScore && (
                      <Badge variant="outline" className="text-xs">
                        {result.confidenceScore}%
                      </Badge>
                    )}
                    {result.imagesGenerated && (
                      <Badge variant="outline" className="text-xs">
                        {result.imagesGenerated} images
                      </Badge>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
