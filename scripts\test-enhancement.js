/**
 * Simple test script to verify enhancement functionality
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testEnhancement() {
  try {
    console.log('🔍 Testing database connection...');
    
    // Test database connection
    const productCount = await prisma.product.count();
    console.log(`✅ Found ${productCount} products in database`);
    
    // Get a sample product with images
    const sampleProduct = await prisma.product.findFirst({
      where: {
        isActive: true,
        images: { isEmpty: false }
      },
      select: {
        id: true,
        name: true,
        brand: true,
        images: true,
        enhancementStatus: true
      }
    });
    
    if (sampleProduct) {
      console.log('📸 Sample product found:');
      console.log(`   Name: ${sampleProduct.name}`);
      console.log(`   Brand: ${sampleProduct.brand}`);
      console.log(`   Images: ${sampleProduct.images.length}`);
      console.log(`   Enhancement Status: ${sampleProduct.enhancementStatus || 'Not processed'}`);
    } else {
      console.log('⚠️  No products with images found');
    }
    
    console.log('✅ Test completed successfully');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testEnhancement();
