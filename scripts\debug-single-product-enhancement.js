#!/usr/bin/env node

/**
 * Debug Single Product Enhancement
 * Tests the enhancement process on a single product to see what's happening
 */

const { PrismaClient } = require('@prisma/client');
const axios = require('axios');

const prisma = new PrismaClient();

async function debugSingleProduct() {
  console.log('🔍 Debug Single Product Enhancement');
  console.log('==================================\n');

  try {
    // Get a sample product
    const product = await prisma.product.findFirst({
      where: {
        images: { isEmpty: false },
        brand: 'Nike' // Focus on Nike products as they should have good search results
      },
      select: {
        id: true,
        name: true,
        brand: true,
        images: true
      }
    });

    if (!product) {
      console.log('❌ No Nike products found for testing');
      return;
    }

    console.log(`🧪 Testing product: ${product.name}`);
    console.log(`🏷️  Brand: ${product.brand}`);
    console.log(`📸 Original image: ${product.images[0]}`);
    console.log(`🆔 Product ID: ${product.id}\n`);

    console.log('🚀 Starting enhancement process...\n');

    // Call the enhancement API
    const response = await axios.post('http://localhost:3000/api/admin/products/enhance-images', {
      imageUrl: product.images[0],
      productId: product.id
    });

    if (!response.data.success) {
      console.log('❌ Enhancement API failed:', response.data.error);
      return;
    }

    const result = response.data.data;

    console.log('📊 Enhancement Results:');
    console.log('======================');
    console.log(`🎯 Confidence Score: ${result.confidence_score}%`);
    console.log(`🖼️  Images Found: ${result.images.length}`);
    console.log(`📋 Image Source: ${result.image_source}`);
    
    if (result.auto_approval_decision) {
      console.log(`🤖 Auto-Approval: ${result.auto_approval_decision.recommendedAction}`);
      console.log(`⚠️  Risk Level: ${result.auto_approval_decision.riskLevel}`);
    }

    console.log('\n📸 Images Details:');
    result.images.forEach((img, index) => {
      console.log(`${index + 1}. Angle: ${img.angle}`);
      console.log(`   Quality: ${img.qualityScore}%`);
      console.log(`   Source: ${img.source}`);
      console.log(`   URL: ${img.url.substring(0, 60)}...`);
      console.log('');
    });

    console.log('📝 Processing Notes:');
    console.log('===================');
    result.processing_notes.forEach((note, index) => {
      console.log(`${index + 1}. ${note}`);
    });

    console.log('\n🔍 Analysis:');
    console.log('============');
    
    if (result.images.length === 1) {
      console.log('⚠️  Only 1 image found - likely fell back to original enhancement');
      console.log('   Possible reasons:');
      console.log('   • Search API returned no results');
      console.log('   • All search results failed quality filtering');
      console.log('   • Error in multi-angle processing');
    } else if (result.images.length > 1) {
      const angles = result.images.map(img => img.angle);
      const uniqueAngles = [...new Set(angles)];
      console.log(`✅ Multi-angle enhancement successful!`);
      console.log(`   Found ${uniqueAngles.length} different angles: ${uniqueAngles.join(', ')}`);
    }

    if (result.image_source === 'enhanced_original') {
      console.log('⚠️  Used fallback enhancement (enhanced original image only)');
    } else if (result.image_source === 'web_fetch') {
      console.log('✅ Used web search enhancement (multi-angle sourcing)');
    }

    // Check quality assessments
    if (result.quality_assessments && result.quality_assessments.length > 0) {
      console.log('\n🎨 Quality Assessments:');
      result.quality_assessments.forEach((qa, index) => {
        console.log(`${index + 1}. Background Score: ${qa.backgroundScore}`);
        console.log(`   Clarity Score: ${qa.clarityScore}`);
        console.log(`   Composition Score: ${qa.compositionScore}`);
        console.log('');
      });
    }

  } catch (error) {
    console.error('❌ Error during debug:', error);
  } finally {
    await prisma.$disconnect();
  }
}

debugSingleProduct().catch(console.error);
