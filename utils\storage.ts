/**
 * Storage utility for handling file uploads
 * Uses UploadThing as the primary storage provider
 */

import { UTApi } from "uploadthing/server";

// Initialize UploadThing API
const utapi = new UTApi();

/**
 * Upload a buffer to UploadThing storage
 * @param buffer - The file buffer to upload
 * @param fileName - The name for the uploaded file
 * @param contentType - The MIME type of the file
 * @returns Promise<string> - The URL of the uploaded file
 */
export async function uploadToS3(
  buffer: Buffer,
  fileName: string,
  contentType: string
): Promise<string> {
  try {
    // Create a File object from the buffer
    const file = new File([buffer], fileName, { type: contentType });
    
    // Upload to UploadThing
    const response = await utapi.uploadFiles([file]);
    
    if (response[0]?.data?.url) {
      return response[0].data.url;
    } else {
      throw new Error('Upload failed: No URL returned');
    }
  } catch (error) {
    console.error('Error uploading to storage:', error);
    throw new Error(`Failed to upload file: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Upload multiple files to UploadThing storage
 * @param files - Array of { buffer: Buffer, fileName: string, contentType: string }
 * @returns Promise<string[]> - Array of URLs of the uploaded files
 */
export async function uploadMultipleFiles(
  files: Array<{ buffer: Buffer; fileName: string; contentType: string }>
): Promise<string[]> {
  try {
    // Convert buffers to File objects
    const fileObjects = files.map(({ buffer, fileName, contentType }) => 
      new File([buffer], fileName, { type: contentType })
    );
    
    // Upload all files
    const responses = await utapi.uploadFiles(fileObjects);
    
    // Extract URLs
    const urls = responses.map(response => {
      if (response.data?.url) {
        return response.data.url;
      } else {
        throw new Error(`Upload failed for file: ${response.error?.message || 'Unknown error'}`);
      }
    });
    
    return urls;
  } catch (error) {
    console.error('Error uploading multiple files:', error);
    throw new Error(`Failed to upload files: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Delete a file from UploadThing storage
 * @param fileKey - The file key or URL to delete
 * @returns Promise<boolean> - Success status
 */
export async function deleteFile(fileKey: string): Promise<boolean> {
  try {
    // Extract file key from URL if needed
    const key = extractFileKey(fileKey);
    
    await utapi.deleteFiles([key]);
    return true;
  } catch (error) {
    console.error('Error deleting file:', error);
    return false;
  }
}

/**
 * Delete multiple files from UploadThing storage
 * @param fileKeys - Array of file keys or URLs to delete
 * @returns Promise<boolean> - Success status
 */
export async function deleteMultipleFiles(fileKeys: string[]): Promise<boolean> {
  try {
    // Extract file keys from URLs if needed
    const keys = fileKeys.map(extractFileKey);
    
    await utapi.deleteFiles(keys);
    return true;
  } catch (error) {
    console.error('Error deleting multiple files:', error);
    return false;
  }
}

/**
 * Extract file key from UploadThing URL
 * @param urlOrKey - The URL or file key
 * @returns string - The file key
 */
function extractFileKey(urlOrKey: string): string {
  // If it's already a key (not a URL), return as is
  if (!urlOrKey.includes('http')) {
    return urlOrKey;
  }
  
  // Extract key from UploadThing URL
  // UploadThing URLs typically look like: https://utfs.io/f/[key]
  const match = urlOrKey.match(/\/f\/([^\/\?]+)/);
  if (match && match[1]) {
    return match[1];
  }
  
  // Fallback: return the last part of the URL
  return urlOrKey.split('/').pop() || urlOrKey;
}
