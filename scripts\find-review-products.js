#!/usr/bin/env node

/**
 * Find Products Marked for Review
 * Identifies and displays detailed information about products needing manual review
 */

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function findReviewProducts() {
  console.log('🔍 Finding Products Marked for Review');
  console.log('====================================\n');

  try {
    // Get products marked for review
    const reviewProducts = await prisma.product.findMany({
      where: {
        enhancementStatus: 'review_required'
      },
      select: {
        id: true,
        name: true,
        brand: true,
        price: true,
        images: true,
        enhancementStatus: true,
        qualityScore: true,
        updatedAt: true,
        createdAt: true
      },
      orderBy: {
        updatedAt: 'desc'
      }
    });

    if (reviewProducts.length === 0) {
      console.log('✅ No products found that require manual review!');
      console.log('   All products have been processed successfully.');
      await prisma.$disconnect();
      return;
    }

    console.log(`📋 Found ${reviewProducts.length} products requiring manual review:\n`);

    reviewProducts.forEach((product, index) => {
      console.log(`${index + 1}. Product Details:`);
      console.log(`   📦 Name: ${product.name}`);
      console.log(`   🏷️  Brand: ${product.brand}`);
      console.log(`   💰 Price: M${product.price}`);
      console.log(`   🆔 ID: ${product.id}`);
      console.log(`   📊 Quality Score: ${product.qualityScore}%`);
      console.log(`   📅 Last Updated: ${product.updatedAt.toLocaleString()}`);
      console.log(`   🖼️  Images: ${product.images.length}`);
      
      if (product.images.length > 0) {
        console.log(`   🔗 First Image: ${product.images[0]}`);
      }
      
      // Explain why it needs review
      if (product.qualityScore < 80) {
        console.log(`   ⚠️  Review Reason: Quality score below 80% threshold`);
      } else {
        console.log(`   ⚠️  Review Reason: Manual review flagged by AI system`);
      }
      
      console.log('');
    });

    // Provide direct links and instructions
    console.log('🎯 How to Review These Products:');
    console.log('===============================');
    console.log('');
    console.log('📱 Option 1: Admin Dashboard');
    console.log('   1. Go to: http://localhost:3000/admin/image-enhancement');
    console.log('   2. Look for the "Manual Review" section');
    console.log('   3. Review each product and approve/reject');
    console.log('');
    console.log('🔗 Option 2: Direct Product Links');
    reviewProducts.forEach((product, index) => {
      console.log(`   ${index + 1}. ${product.name}: http://localhost:3000/admin/products/${product.id}`);
    });
    console.log('');
    console.log('⚡ Option 3: Auto-Approve All (if you trust the results)');
    console.log('   Run: node scripts/auto-approve-review-products.js');
    console.log('');
    console.log('🔄 Option 4: Re-enhance with Higher Confidence');
    console.log('   Run: node scripts/re-enhance-review-products.js');

  } catch (error) {
    console.error('❌ Error finding review products:', error);
  } finally {
    await prisma.$disconnect();
  }
}

findReviewProducts().catch(console.error);
