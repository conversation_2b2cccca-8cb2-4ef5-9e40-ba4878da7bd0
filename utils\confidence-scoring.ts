/**
 * Advanced Confidence Scoring System for AI Image Enhancement
 * Implements sophisticated scoring algorithms to determine auto-approval eligibility
 */

interface ProductMetadata {
  productType: string;
  brand: string;
  modelName: string;
  colorway: string;
  sku?: string;
  confidence: number;
}

interface ImageQualityAssessment {
  backgroundScore: number;
  clarityScore: number;
  compositionScore: number;
  overallScore: number;
  hasWatermark: boolean;
  hasForeignElements: boolean;
  estimatedAngle?: string;
}

interface EnhancementResult {
  product_name: string;
  image_source: 'web_fetch' | 'enhanced_original';
  images: Array<{
    angle: string;
    url: string;
    qualityScore: number;
    source: string;
  }>;
  confidence_score: number;
  processing_notes: string[];
}

interface ConfidenceFactors {
  productIdentification: number;
  imageQuality: number;
  sourceReliability: number;
  angleCompleteness: number;
  brandRecognition: number;
  consistencyScore: number;
}

interface AutoApprovalDecision {
  shouldAutoApprove: boolean;
  confidence: number;
  factors: ConfidenceFactors;
  reasoning: string[];
  riskLevel: 'low' | 'medium' | 'high';
  recommendedAction: 'auto_approve' | 'manual_review' | 'reject';
}

/**
 * Calculate comprehensive confidence score for enhancement results
 */
export function calculateConfidenceScore(
  metadata: ProductMetadata,
  enhancementResult: EnhancementResult,
  qualityAssessments: ImageQualityAssessment[]
): ConfidenceFactors {
  
  // Factor 1: Product Identification Confidence (0-100)
  const productIdentification = calculateProductIdentificationScore(metadata);
  
  // Factor 2: Image Quality Score (0-100)
  const imageQuality = calculateImageQualityScore(qualityAssessments);
  
  // Factor 3: Source Reliability (0-100)
  const sourceReliability = calculateSourceReliabilityScore(enhancementResult);
  
  // Factor 4: Angle Completeness (0-100)
  const angleCompleteness = calculateAngleCompletenessScore(enhancementResult.images);
  
  // Factor 5: Brand Recognition (0-100)
  const brandRecognition = calculateBrandRecognitionScore(metadata.brand);
  
  // Factor 6: Consistency Score (0-100)
  const consistencyScore = calculateConsistencyScore(enhancementResult.images, qualityAssessments);
  
  return {
    productIdentification,
    imageQuality,
    sourceReliability,
    angleCompleteness,
    brandRecognition,
    consistencyScore
  };
}

/**
 * Make auto-approval decision based on confidence factors
 */
export function makeAutoApprovalDecision(
  factors: ConfidenceFactors,
  thresholds: {
    autoApprove: number;
    manualReview: number;
    reject: number;
  } = {
    autoApprove: 85,
    manualReview: 60,
    reject: 30
  }
): AutoApprovalDecision {
  
  // Calculate weighted confidence score
  const weights = {
    productIdentification: 0.20,
    imageQuality: 0.25,
    sourceReliability: 0.20,
    angleCompleteness: 0.15,
    brandRecognition: 0.10,
    consistencyScore: 0.10
  };
  
  const confidence = Math.round(
    factors.productIdentification * weights.productIdentification +
    factors.imageQuality * weights.imageQuality +
    factors.sourceReliability * weights.sourceReliability +
    factors.angleCompleteness * weights.angleCompleteness +
    factors.brandRecognition * weights.brandRecognition +
    factors.consistencyScore * weights.consistencyScore
  );
  
  const reasoning: string[] = [];
  let riskLevel: 'low' | 'medium' | 'high' = 'medium';
  let recommendedAction: 'auto_approve' | 'manual_review' | 'reject' = 'manual_review';
  
  // Determine risk level and action
  if (confidence >= thresholds.autoApprove) {
    riskLevel = 'low';
    recommendedAction = 'auto_approve';
    reasoning.push(`High confidence score (${confidence}%) meets auto-approval threshold`);
    
    // Additional checks for auto-approval
    if (factors.imageQuality < 80) {
      reasoning.push('⚠️ Image quality below optimal threshold - consider manual review');
      recommendedAction = 'manual_review';
      riskLevel = 'medium';
    }
    
    if (factors.sourceReliability < 70) {
      reasoning.push('⚠️ Source reliability concerns - manual review recommended');
      recommendedAction = 'manual_review';
      riskLevel = 'medium';
    }
    
  } else if (confidence >= thresholds.manualReview) {
    riskLevel = 'medium';
    recommendedAction = 'manual_review';
    reasoning.push(`Moderate confidence (${confidence}%) requires manual review`);
    
    if (factors.productIdentification < 60) {
      reasoning.push('Low product identification confidence');
    }
    if (factors.imageQuality < 70) {
      reasoning.push('Image quality needs verification');
    }
    
  } else {
    riskLevel = 'high';
    recommendedAction = 'reject';
    reasoning.push(`Low confidence (${confidence}%) - enhancement not recommended`);
    
    if (factors.productIdentification < 40) {
      reasoning.push('Product identification failed');
    }
    if (factors.imageQuality < 50) {
      reasoning.push('Poor image quality detected');
    }
  }
  
  // Add factor-specific insights
  if (factors.angleCompleteness >= 80) {
    reasoning.push('✅ Excellent angle coverage');
  } else if (factors.angleCompleteness < 40) {
    reasoning.push('⚠️ Limited viewing angles available');
  }
  
  if (factors.brandRecognition >= 90) {
    reasoning.push('✅ Well-known brand with high recognition');
  }
  
  if (factors.consistencyScore < 60) {
    reasoning.push('⚠️ Inconsistent image quality across angles');
  }
  
  return {
    shouldAutoApprove: recommendedAction === 'auto_approve',
    confidence,
    factors,
    reasoning,
    riskLevel,
    recommendedAction
  };
}

/**
 * Calculate product identification confidence
 */
function calculateProductIdentificationScore(metadata: ProductMetadata): number {
  let score = metadata.confidence;
  
  // Boost score for known brands
  const knownBrands = ['nike', 'adidas', 'puma', 'jordan', 'converse', 'vans', 'new balance'];
  if (knownBrands.includes(metadata.brand.toLowerCase())) {
    score += 10;
  }
  
  // Boost score if model name is specific
  if (metadata.modelName !== 'unknown' && metadata.modelName.length > 3) {
    score += 5;
  }
  
  // Boost score if colorway is identified
  if (metadata.colorway !== 'unknown' && metadata.colorway.length > 3) {
    score += 5;
  }
  
  // Boost score if SKU is found
  if (metadata.sku) {
    score += 10;
  }
  
  return Math.min(100, score);
}

/**
 * Calculate average image quality score
 */
function calculateImageQualityScore(assessments: ImageQualityAssessment[]): number {
  if (assessments.length === 0) return 0;
  
  const avgScore = assessments.reduce((sum, assessment) => sum + assessment.overallScore, 0) / assessments.length;
  
  // Penalty for watermarks or foreign elements
  const hasIssues = assessments.some(a => a.hasWatermark || a.hasForeignElements);
  if (hasIssues) {
    return Math.max(0, avgScore - 15);
  }
  
  return avgScore;
}

/**
 * Calculate source reliability score
 */
function calculateSourceReliabilityScore(result: EnhancementResult): number {
  if (result.image_source === 'web_fetch') {
    // Higher score for web-fetched images
    const imageCount = result.images.length;
    let score = 70; // Base score for web fetch
    
    // Bonus for multiple images
    if (imageCount >= 3) score += 20;
    else if (imageCount >= 2) score += 10;
    
    // Bonus for high-quality sources
    const avgQuality = result.images.reduce((sum, img) => sum + img.qualityScore, 0) / result.images.length;
    if (avgQuality >= 85) score += 10;
    
    return Math.min(100, score);
  } else {
    // Lower score for enhanced original
    return 50;
  }
}

/**
 * Calculate angle completeness score
 */
function calculateAngleCompletenessScore(images: Array<{angle: string}>): number {
  const desiredAngles = ['front', 'side_left', 'side_right', 'back', 'top', 'sole'];
  const availableAngles = new Set(images.map(img => img.angle));
  
  const coverage = (availableAngles.size / desiredAngles.length) * 100;
  
  // Bonus for having key angles
  let bonus = 0;
  if (availableAngles.has('front')) bonus += 10;
  if (availableAngles.has('side_left') || availableAngles.has('side_right')) bonus += 10;
  
  return Math.min(100, coverage + bonus);
}

/**
 * Calculate brand recognition score
 */
function calculateBrandRecognitionScore(brand: string): number {
  const brandLower = brand.toLowerCase();
  
  // Tier 1 brands (highest recognition)
  const tier1Brands = ['nike', 'adidas', 'jordan'];
  if (tier1Brands.includes(brandLower)) return 100;
  
  // Tier 2 brands (high recognition)
  const tier2Brands = ['puma', 'converse', 'vans', 'new balance', 'reebok'];
  if (tier2Brands.includes(brandLower)) return 85;
  
  // Tier 3 brands (moderate recognition)
  const tier3Brands = ['asics', 'skechers', 'fila', 'under armour'];
  if (tier3Brands.includes(brandLower)) return 70;
  
  // Unknown or less common brands
  if (brand !== 'unknown' && brand.length > 2) return 50;
  
  return 25;
}

/**
 * Calculate consistency score across images
 */
function calculateConsistencyScore(
  images: Array<{qualityScore: number}>,
  assessments: ImageQualityAssessment[]
): number {
  if (images.length <= 1) return 100; // Single image is consistent by definition
  
  const qualityScores = images.map(img => img.qualityScore);
  const mean = qualityScores.reduce((sum, score) => sum + score, 0) / qualityScores.length;
  
  // Calculate standard deviation
  const variance = qualityScores.reduce((sum, score) => sum + Math.pow(score - mean, 2), 0) / qualityScores.length;
  const stdDev = Math.sqrt(variance);
  
  // Lower standard deviation = higher consistency
  const consistencyScore = Math.max(0, 100 - (stdDev * 2));
  
  return Math.round(consistencyScore);
}
