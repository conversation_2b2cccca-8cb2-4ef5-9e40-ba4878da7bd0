# 🚀 AI Image Enhancement Setup & Usage Guide

## 📋 Prerequisites Checklist

### ✅ **Required Environment Variables**
Make sure these are in your `.env` file:

```env
# Google AI (Gemini)
GOOGLE_AI_API_KEY=your_gemini_api_key

# Google Custom Search
GOOGLE_CUSTOM_SEARCH_API_KEY=your_google_search_api_key
GOOGLE_CUSTOM_SEARCH_ENGINE_ID=your_search_engine_id

# UploadThing (for image storage)
UPLOADTHING_SECRET=your_uploadthing_secret
UPLOADTHING_APP_ID=your_uploadthing_app_id

# Optional: Search limits
SEARCH_RESULTS_LIMIT=20
```

### 🔧 **Google Custom Search Engine Optimization**

1. **Go to**: [Google Custom Search Console](https://cse.google.com/cse/)
2. **Edit your search engine** with these settings:

#### **Sites to Include (High-Quality Sources):**
```
nike.com
adidas.com
jordan.com
puma.com
converse.com
vans.com
newbalance.com
reebok.com
footlocker.com
eastbay.com
finishline.com
jdsports.com
size.com
sneakersnstuff.com
endclothing.com
stockx.com
goat.com
flightclub.com
stadiumgoods.com
solecollector.com
hypebeast.com
sneakernews.com
```

#### **Sites to Exclude (Low-Quality Sources):**
```
pinterest.com
instagram.com
facebook.com
twitter.com
reddit.com
tumblr.com
flickr.com
aliexpress.com
wish.com
dhgate.com
```

#### **Search Engine Settings:**
- ✅ **Image Search**: Enabled
- ✅ **SafeSearch**: Moderate
- ✅ **Country**: Global or your target markets
- ✅ **Language**: English + your target languages

## 🧪 **Step 1: Verify Setup**

Run the verification script to check everything is working:

```bash
pnpm exec ts-node scripts/verify-enhancement-setup.ts
```

This will check:
- ✅ Environment variables
- ✅ Gemini AI connection
- ✅ Google Custom Search API
- ✅ Database connection
- ✅ Image processing capabilities
- ✅ File system permissions

## 🚀 **Step 2: Test with a Few Products**

Start with a small test to see the system in action:

```bash
pnpm exec ts-node scripts/quick-enhance.ts
```

This will:
- Process the first 5 products that need enhancement
- Show real-time progress and results
- Give you confidence scores and decisions

## 👀 **Step 3: Preview Full Enhancement**

See what would be enhanced without making changes:

```bash
pnpm exec ts-node scripts/enhance-all-existing-products.ts --dry-run
```

This shows you:
- How many products would be processed
- Which products would be enhanced
- No actual changes made

## 🎯 **Step 4: Run Full Enhancement**

When you're ready, enhance all your existing products:

```bash
# Basic run (recommended settings)
pnpm exec ts-node scripts/enhance-all-existing-products.ts

# Custom settings
pnpm exec ts-node scripts/enhance-all-existing-products.ts \
  --batch-size=5 \
  --confidence-threshold=85 \
  --auto-approve \
  --verbose
```

### **Script Options:**
- `--dry-run`: Preview only, no changes
- `--batch-size=N`: Process N products at once (default: 3)
- `--confidence-threshold=N`: Auto-approve threshold (default: 80)
- `--filter=TYPE`: Filter products (`all`, `pending`, `failed`, `no-images`)
- `--auto-approve`: Enable auto-approval (default: true)
- `--verbose`: Detailed logging

## 📊 **Understanding the Results**

### **Enhancement Status:**
- ✅ **completed**: Auto-approved and applied
- ⚠️ **review_required**: Needs manual review
- ❌ **failed**: No suitable images found
- 🔄 **processing**: Currently being processed

### **Confidence Scores:**
- **85%+**: Auto-approved (high confidence)
- **60-84%**: Manual review required
- **<60%**: Enhancement not recommended

### **Risk Levels:**
- 🟢 **Low Risk**: Safe for auto-approval
- 🟡 **Medium Risk**: Manual review recommended
- 🔴 **High Risk**: Careful review needed

## 🎛️ **Admin Dashboard Usage**

### **Individual Product Enhancement:**
1. Go to product management
2. Find product with images
3. Click "Enhance Images"
4. Review confidence analysis
5. Approve or request changes

### **Batch Processing:**
1. Navigate to Image Enhancement Dashboard
2. Configure batch settings
3. Select products or "Process All"
4. Monitor progress in real-time
5. Review results and manual review items

### **Manual Review Process:**
1. Check products with status "review_required"
2. View confidence breakdown and AI reasoning
3. Compare original vs enhanced images
4. Approve, reject, or re-process

## 🔧 **Troubleshooting**

### **Common Issues:**

#### **"No images found" errors:**
- Check Google Custom Search API quota
- Verify search engine configuration
- Ensure product metadata is accurate

#### **"Enhancement failed" errors:**
- Check Gemini AI API quota
- Verify image URLs are accessible
- Check network connectivity

#### **Low confidence scores:**
- Review product identification accuracy
- Check if brand is in recognition database
- Verify image quality of originals

#### **API quota exceeded:**
- Check Google Cloud Console for quotas
- Consider upgrading API limits
- Reduce batch size or add delays

### **Performance Optimization:**
- Use smaller batch sizes for better stability
- Process during off-peak hours
- Monitor API usage and quotas
- Consider processing in stages

## 📈 **Monitoring & Maintenance**

### **Regular Checks:**
- Monitor enhancement success rates
- Review manual review queue
- Check API quota usage
- Update search engine sources

### **Quality Improvement:**
- Analyze failed enhancements
- Refine confidence thresholds
- Update brand recognition database
- Optimize search engine configuration

## 🎯 **Best Practices**

### **For Best Results:**
1. **Start Small**: Test with quick-enhance script first
2. **Monitor Progress**: Watch the first few batches closely
3. **Review Settings**: Adjust confidence thresholds based on results
4. **Quality Control**: Regularly review auto-approved items
5. **Update Sources**: Keep search engine sources current

### **Recommended Workflow:**
1. Run verification script
2. Test with 5 products (quick-enhance)
3. Preview full run (dry-run)
4. Process in batches during off-peak hours
5. Review manual review items daily
6. Monitor and adjust settings

## 🆘 **Getting Help**

If you encounter issues:
1. Run the verification script first
2. Check the troubleshooting section
3. Review API quotas and billing
4. Check the processing logs for specific errors
5. Consider reducing batch size or confidence threshold

---

**Ready to enhance your product images with AI? Start with the verification script!**

```bash
pnpm exec ts-node scripts/verify-enhancement-setup.ts
```
