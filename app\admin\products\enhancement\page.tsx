'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { 
  Sparkles, 
  Image as ImageIcon, 
  TrendingUp, 
  AlertCircle, 
  CheckCircle,
  Clock,
  Search,
  RefreshCw,
  Download,
  Settings
} from 'lucide-react';
import { toast } from 'sonner';

interface EnhancementStats {
  total: number;
  enhanced: number;
  pending: number;
  failed: number;
  averageQuality: number;
  lastRun?: string;
}

interface EnhancementJob {
  id: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress: number;
  startedAt?: string;
  completedAt?: string;
  stats?: {
    processed: number;
    enhanced: number;
    failed: number;
  };
}

export default function ProductEnhancementPage() {
  const [stats, setStats] = useState<EnhancementStats>({
    total: 0,
    enhanced: 0,
    pending: 0,
    failed: 0,
    averageQuality: 0
  });
  
  const [currentJob, setCurrentJob] = useState<EnhancementJob | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [settings, setSettings] = useState({
    batchSize: 10,
    autoEnhance: true,
    qualityThreshold: 70,
    skipRecent: true
  });

  useEffect(() => {
    fetchStats();
    const interval = setInterval(fetchStats, 5000); // Refresh every 5 seconds
    return () => clearInterval(interval);
  }, []);

  const fetchStats = async () => {
    try {
      const response = await fetch('/api/admin/products/enhancement/stats');
      if (response.ok) {
        const data = await response.json();
        setStats(data.stats);
        setCurrentJob(data.currentJob);
      }
    } catch (error) {
      console.error('Failed to fetch enhancement stats:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const startBulkEnhancement = async () => {
    try {
      const response = await fetch('/api/admin/products/enhancement/bulk', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(settings)
      });

      if (response.ok) {
        const data = await response.json();
        setCurrentJob(data.job);
        toast.success('Bulk enhancement started!');
      } else {
        const error = await response.json();
        toast.error(error.message || 'Failed to start enhancement');
      }
    } catch (error) {
      toast.error('Failed to start bulk enhancement');
    }
  };

  const stopEnhancement = async () => {
    try {
      const response = await fetch('/api/admin/products/enhancement/stop', {
        method: 'POST'
      });

      if (response.ok) {
        setCurrentJob(null);
        toast.success('Enhancement stopped');
        fetchStats();
      }
    } catch (error) {
      toast.error('Failed to stop enhancement');
    }
  };

  const exportReport = async () => {
    try {
      const response = await fetch('/api/admin/products/enhancement/export');
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `enhancement-report-${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        toast.success('Report exported successfully');
      }
    } catch (error) {
      toast.error('Failed to export report');
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="w-8 h-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">AI Image Enhancement</h1>
          <p className="text-muted-foreground">
            Manage and monitor AI-powered product image enhancement
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={exportReport}>
            <Download className="w-4 h-4 mr-2" />
            Export Report
          </Button>
          <Button onClick={fetchStats}>
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Products</p>
                <p className="text-2xl font-bold">{stats.total.toLocaleString()}</p>
              </div>
              <ImageIcon className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Enhanced</p>
                <p className="text-2xl font-bold text-green-600">{stats.enhanced.toLocaleString()}</p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Pending</p>
                <p className="text-2xl font-bold text-yellow-600">{stats.pending.toLocaleString()}</p>
              </div>
              <Clock className="w-8 h-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Avg Quality</p>
                <p className="text-2xl font-bold text-purple-600">{stats.averageQuality.toFixed(1)}%</p>
              </div>
              <TrendingUp className="w-8 h-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Current Job Status */}
      {currentJob && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Sparkles className="w-5 h-5" />
              Enhancement Job Status
              <Badge variant={
                currentJob.status === 'running' ? 'default' :
                currentJob.status === 'completed' ? 'secondary' :
                currentJob.status === 'failed' ? 'destructive' : 'outline'
              }>
                {currentJob.status}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Progress</span>
                <span>{currentJob.progress}%</span>
              </div>
              <Progress value={currentJob.progress} />
            </div>
            
            {currentJob.stats && (
              <div className="grid grid-cols-3 gap-4 text-sm">
                <div>
                  <p className="font-medium">Processed</p>
                  <p className="text-2xl font-bold">{currentJob.stats.processed}</p>
                </div>
                <div>
                  <p className="font-medium">Enhanced</p>
                  <p className="text-2xl font-bold text-green-600">{currentJob.stats.enhanced}</p>
                </div>
                <div>
                  <p className="font-medium">Failed</p>
                  <p className="text-2xl font-bold text-red-600">{currentJob.stats.failed}</p>
                </div>
              </div>
            )}
            
            {currentJob.status === 'running' && (
              <Button variant="destructive" onClick={stopEnhancement}>
                Stop Enhancement
              </Button>
            )}
          </CardContent>
        </Card>
      )}

      {/* Enhancement Settings & Controls */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="w-5 h-5" />
              Enhancement Settings
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="batchSize">Batch Size</Label>
              <Input
                id="batchSize"
                type="number"
                value={settings.batchSize}
                onChange={(e) => setSettings(prev => ({ ...prev, batchSize: parseInt(e.target.value) }))}
                min="1"
                max="50"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="qualityThreshold">Quality Threshold (%)</Label>
              <Input
                id="qualityThreshold"
                type="number"
                value={settings.qualityThreshold}
                onChange={(e) => setSettings(prev => ({ ...prev, qualityThreshold: parseInt(e.target.value) }))}
                min="0"
                max="100"
              />
            </div>
            
            <div className="flex items-center space-x-2">
              <Switch
                id="autoEnhance"
                checked={settings.autoEnhance}
                onCheckedChange={(checked) => setSettings(prev => ({ ...prev, autoEnhance: checked }))}
              />
              <Label htmlFor="autoEnhance">Auto-enhance new uploads</Label>
            </div>
            
            <div className="flex items-center space-x-2">
              <Switch
                id="skipRecent"
                checked={settings.skipRecent}
                onCheckedChange={(checked) => setSettings(prev => ({ ...prev, skipRecent: checked }))}
              />
              <Label htmlFor="skipRecent">Skip recently enhanced products</Label>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Bulk Enhancement</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-muted-foreground">
              Process all products that need enhancement. This will analyze images, 
              search for better quality versions, and apply AI enhancements.
            </p>
            
            <div className="space-y-2">
              <p className="text-sm font-medium">What will be processed:</p>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Products without AI analysis</li>
                <li>• Failed enhancement attempts</li>
                <li>• Products with low quality scores</li>
                {!settings.skipRecent && <li>• All products (including recent)</li>}
              </ul>
            </div>
            
            <Button 
              onClick={startBulkEnhancement} 
              disabled={currentJob?.status === 'running'}
              className="w-full"
            >
              <Sparkles className="w-4 h-4 mr-2" />
              Start Bulk Enhancement
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
