import prisma from '../lib/prisma';
import { enhanceProductImages } from '../utils/advanced-image-processor';

async function main() {
  const products = await prisma.product.findMany();
  console.log(`Found ${products.length} products.`);

  for (const product of products) {
    if (!product.images || product.images.length === 0) {
      console.log(`Skipping product ${product.id} (${product.name}) - no images.`);
      continue;
    }
    const originalImageUrl = product.images[0];
    try {
      console.log(`🚀 Processing product ${product.id} (${product.name})...`);

      // Mark as processing
      await prisma.product.update({
        where: { id: product.id },
        data: { enhancementStatus: 'processing' }
      });

      // Run the enhanced AI processing
      const enhancementResult = await enhanceProductImages(originalImageUrl, product.id);

      // Check auto-approval decision
      const autoApprovalDecision = enhancementResult.auto_approval_decision;
      const shouldAutoApprove = autoApprovalDecision?.shouldAutoApprove &&
                               autoApprovalDecision.riskLevel === 'low' &&
                               enhancementResult.confidence_score >= 80;

      if (shouldAutoApprove && enhancementResult.images.length > 0) {
        // Auto-approve and update
        await prisma.product.update({
          where: { id: product.id },
          data: {
            images: enhancementResult.images.map(img => img.url),
            enhancementStatus: 'completed',
            qualityScore: enhancementResult.confidence_score
          }
        });
        console.log(`✅ Auto-approved product ${product.id} with ${enhancementResult.images.length} enhanced images (${enhancementResult.confidence_score}% confidence).`);
      } else if (enhancementResult.images.length > 0) {
        // Mark for manual review
        await prisma.product.update({
          where: { id: product.id },
          data: {
            enhancementStatus: 'review_required',
            qualityScore: enhancementResult.confidence_score
          }
        });
        console.log(`⚠️ Product ${product.id} requires manual review (${enhancementResult.confidence_score}% confidence, ${autoApprovalDecision?.riskLevel} risk).`);
      } else {
        // No images found
        await prisma.product.update({
          where: { id: product.id },
          data: { enhancementStatus: 'failed' }
        });
        console.log(`❌ No enhanced images found for product ${product.id}.`);
      }
    } catch (err) {
      console.error(`❌ Error processing product ${product.id}:`, err);
      // Mark as failed
      await prisma.product.update({
        where: { id: product.id },
        data: { enhancementStatus: 'failed' }
      });
    }
  }
  console.log('Done.');
}

main().catch((err) => {
  console.error('Fatal error:', err);
  process.exit(1);
}); 