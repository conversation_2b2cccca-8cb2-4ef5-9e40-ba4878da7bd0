import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth-utils';
import prisma from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    // Check authentication and admin role
    const user = await getCurrentUser();
    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');
    const status = searchParams.get('status');

    // Build where clause
    const where: any = {};
    if (status && status !== 'all') {
      where.status = status;
    }

    // Get enhancement logs with product information
    const logs = await prisma.imageEnhancementLog.findMany({
      where,
      include: {
        product: {
          select: {
            name: true,
            brand: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: limit,
      skip: offset
    });

    // Transform the data for the frontend
    const transformedLogs = logs.map(log => ({
      id: log.id,
      productId: log.productId,
      productName: log.product?.name || 'Unknown Product',
      productBrand: log.product?.brand || 'Unknown Brand',
      originalImageUrl: log.originalImageUrl,
      enhancedImageUrl: log.enhancedImageUrl,
      status: log.status,
      qualityScore: log.qualityScore,
      processingTime: log.processingTime,
      createdAt: log.createdAt.toISOString(),
      completedAt: log.completedAt?.toISOString(),
      errorMessage: log.errorMessage
    }));

    // Get total count for pagination
    const totalCount = await prisma.imageEnhancementLog.count({ where });

    return NextResponse.json({
      logs: transformedLogs,
      pagination: {
        total: totalCount,
        limit,
        offset,
        hasMore: offset + limit < totalCount
      }
    });

  } catch (error) {
    console.error('Error fetching enhancement logs:', error);
    return NextResponse.json(
      { error: 'Failed to fetch enhancement logs' },
      { status: 500 }
    );
  }
}
