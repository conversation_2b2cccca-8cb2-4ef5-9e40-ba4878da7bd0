"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import {
  ImageIcon,
  Play,
  Pause,
  RefreshCw,
  CheckCircle,
  XCircle,
  Clock,
  TrendingUp,
  BarChart3,
  Settings,
  AlertTriangle,
  Zap,
  Eye,
  Download
} from "lucide-react";
import { toast } from "sonner";

interface EnhancementStats {
  total: number;
  completed: number;
  processing: number;
  failed: number;
  pending: number;
  averageQualityScore: number;
  totalProcessingTime: number;
}

interface EnhancementLog {
  id: string;
  productId: string;
  productName: string;
  originalImageUrl: string;
  enhancedImageUrl?: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  qualityScore?: number;
  processingTime?: number;
  createdAt: string;
  completedAt?: string;
  errorMessage?: string;
}

export default function ImageEnhancementDashboard() {
  const [stats, setStats] = useState<EnhancementStats>({
    total: 0,
    completed: 0,
    processing: 0,
    failed: 0,
    pending: 0,
    averageQualityScore: 0,
    totalProcessingTime: 0
  });
  
  const [logs, setLogs] = useState<EnhancementLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [isProcessing, setIsProcessing] = useState(false);
  const [activeTab, setActiveTab] = useState("overview");

  useEffect(() => {
    fetchStats();
    fetchLogs();
    
    // Set up polling for real-time updates
    const interval = setInterval(() => {
      fetchStats();
      fetchLogs();
    }, 5000);
    
    return () => clearInterval(interval);
  }, []);

  const fetchStats = async () => {
    try {
      // Get enhancement configuration and stats
      const response = await fetch('/api/admin/products/enhance-images');
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.data.stats) {
          setStats({
            total: data.data.stats.recent_enhancements + data.data.stats.pending_enhancements,
            completed: data.data.stats.recent_enhancements,
            processing: data.data.stats.pending_enhancements,
            failed: 0, // Will be updated when we have proper tracking
            pending: 0,
            averageQualityScore: 85, // Default value
            totalProcessingTime: 0
          });
        }
      }

      // Also fetch product stats for more accurate data
      const productsResponse = await fetch('/api/admin/products');
      if (productsResponse.ok) {
        const productsData = await productsResponse.json();
        if (productsData.success && productsData.data) {
          const products = productsData.data;
          const enhancementStats = {
            total: products.length,
            completed: products.filter((p: any) => p.enhancementStatus === 'completed').length,
            processing: products.filter((p: any) => p.enhancementStatus === 'processing').length,
            failed: products.filter((p: any) => p.enhancementStatus === 'failed').length,
            pending: products.filter((p: any) => p.enhancementStatus === 'pending' || !p.enhancementStatus).length,
            averageQualityScore: products
              .filter((p: any) => p.qualityScore)
              .reduce((sum: number, p: any) => sum + p.qualityScore, 0) /
              products.filter((p: any) => p.qualityScore).length || 0,
            totalProcessingTime: 0
          };
          setStats(enhancementStats);
        }
      }
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  const fetchLogs = async () => {
    try {
      // Fetch recent products with enhancement status for logs
      const response = await fetch('/api/admin/products?limit=50');
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.data) {
          const products = data.data;
          const enhancementLogs = products
            .filter((p: any) => p.enhancementStatus)
            .map((p: any) => ({
              id: p.id,
              productId: p.id,
              productName: p.name,
              originalImageUrl: p.images?.[0] || '',
              enhancedImageUrl: p.images?.[0] || '',
              status: p.enhancementStatus || 'pending',
              qualityScore: p.qualityScore,
              processingTime: 0,
              createdAt: p.updatedAt || p.createdAt,
              completedAt: p.enhancementStatus === 'completed' ? p.updatedAt : undefined,
              errorMessage: p.enhancementStatus === 'failed' ? 'Enhancement failed' : undefined
            }))
            .sort((a: any, b: any) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

          setLogs(enhancementLogs);
        }
      }
    } catch (error) {
      console.error('Error fetching logs:', error);
    } finally {
      setLoading(false);
    }
  };

  const startBulkEnhancement = async () => {
    setIsProcessing(true);
    try {
      const response = await fetch('/api/admin/products/batch-enhance', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          processAll: true,
          autoApprove: true,
          confidenceThreshold: 80,
          maxConcurrent: 3
        })
      });

      if (response.ok) {
        const data = await response.json();
        toast.success(`Bulk enhancement started for ${data.data.total} products`);
        fetchStats();
        fetchLogs();
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || 'Failed to start bulk enhancement');
      }
    } catch (error) {
      toast.error('Error starting bulk enhancement');
      console.error('Bulk enhancement error:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const retryFailedEnhancements = async () => {
    try {
      // Get failed products and retry them
      const productsResponse = await fetch('/api/admin/products');
      if (!productsResponse.ok) {
        throw new Error('Failed to fetch products');
      }

      const productsData = await productsResponse.json();
      const failedProducts = productsData.data.filter((p: any) => p.enhancementStatus === 'failed');

      if (failedProducts.length === 0) {
        toast.info('No failed enhancements to retry');
        return;
      }

      const response = await fetch('/api/admin/products/batch-enhance', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          productIds: failedProducts.map((p: any) => p.id),
          autoApprove: false, // Manual review for retries
          confidenceThreshold: 70, // Lower threshold for retries
          maxConcurrent: 2
        })
      });

      if (response.ok) {
        toast.success(`Retrying ${failedProducts.length} failed enhancements`);
        fetchStats();
        fetchLogs();
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || 'Failed to retry enhancements');
      }
    } catch (error) {
      toast.error('Error retrying enhancements');
      console.error('Retry error:', error);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge variant="default" className="bg-green-100 text-green-800"><CheckCircle className="w-3 h-3 mr-1" />Completed</Badge>;
      case 'processing':
        return <Badge variant="default" className="bg-blue-100 text-blue-800"><Clock className="w-3 h-3 mr-1" />Processing</Badge>;
      case 'failed':
        return <Badge variant="destructive"><XCircle className="w-3 h-3 mr-1" />Failed</Badge>;
      case 'pending':
        return <Badge variant="outline"><Clock className="w-3 h-3 mr-1" />Pending</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const completionRate = stats.total > 0 ? (stats.completed / stats.total) * 100 : 0;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Image Enhancement Dashboard</h1>
          <p className="text-gray-600 mt-2">Monitor and manage AI-powered product image enhancement</p>
        </div>
        <div className="flex gap-2">
          <Button 
            onClick={startBulkEnhancement} 
            disabled={isProcessing}
            className="flex items-center gap-2"
          >
            {isProcessing ? <RefreshCw className="w-4 h-4 animate-spin" /> : <Play className="w-4 h-4" />}
            {isProcessing ? 'Processing...' : 'Start Bulk Enhancement'}
          </Button>
          <Button 
            variant="outline" 
            onClick={retryFailedEnhancements}
            className="flex items-center gap-2"
          >
            <RefreshCw className="w-4 h-4" />
            Retry Failed
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Products</p>
                <p className="text-3xl font-bold text-gray-900">{stats.total}</p>
              </div>
              <ImageIcon className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Completed</p>
                <p className="text-3xl font-bold text-green-600">{stats.completed}</p>
                <p className="text-xs text-gray-500">{completionRate.toFixed(1)}% complete</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Average Quality</p>
                <p className="text-3xl font-bold text-purple-600">{stats.averageQualityScore.toFixed(0)}%</p>
              </div>
              <TrendingUp className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Failed</p>
                <p className="text-3xl font-bold text-red-600">{stats.failed}</p>
                {stats.failed > 0 && (
                  <p className="text-xs text-red-500">Needs attention</p>
                )}
              </div>
              <XCircle className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Progress Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="w-5 h-5" />
            Enhancement Progress
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <div className="flex justify-between text-sm mb-2">
                <span>Overall Progress</span>
                <span>{stats.completed}/{stats.total} ({completionRate.toFixed(1)}%)</span>
              </div>
              <Progress value={completionRate} className="h-2" />
            </div>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{stats.processing}</div>
                <div className="text-gray-600">Processing</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-600">{stats.pending}</div>
                <div className="text-gray-600">Pending</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{stats.completed}</div>
                <div className="text-gray-600">Completed</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">{stats.failed}</div>
                <div className="text-gray-600">Failed</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tabs for detailed views */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="logs">Enhancement Logs</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {stats.failed > 0 && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                {stats.failed} products failed enhancement. Consider reviewing the logs and retrying.
              </AlertDescription>
            </Alert>
          )}
          
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Button variant="outline" className="flex items-center gap-2">
                  <Eye className="w-4 h-4" />
                  Preview Enhancements
                </Button>
                <Button variant="outline" className="flex items-center gap-2">
                  <Download className="w-4 h-4" />
                  Export Report
                </Button>
                <Button variant="outline" className="flex items-center gap-2">
                  <Settings className="w-4 h-4" />
                  Configure Settings
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="logs" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Enhancement Logs</CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="text-center py-8">Loading logs...</div>
              ) : logs.length === 0 ? (
                <div className="text-center py-8 text-gray-500">No enhancement logs found</div>
              ) : (
                <div className="space-y-4">
                  {logs.slice(0, 10).map((log) => (
                    <div key={log.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex-1">
                        <div className="font-medium">{log.productName}</div>
                        <div className="text-sm text-gray-500">
                          {new Date(log.createdAt).toLocaleString()}
                        </div>
                        {log.errorMessage && (
                          <div className="text-sm text-red-600 mt-1">{log.errorMessage}</div>
                        )}
                      </div>
                      <div className="flex items-center gap-4">
                        {log.qualityScore && (
                          <div className="text-sm">
                            <span className="font-medium">{log.qualityScore}%</span>
                            <span className="text-gray-500"> quality</span>
                          </div>
                        )}
                        {getStatusBadge(log.status)}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Enhancement Settings</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="text-sm text-gray-600">
                  Configure image enhancement parameters and processing options.
                </div>
                <Button variant="outline" className="flex items-center gap-2">
                  <Settings className="w-4 h-4" />
                  Configure Enhancement Settings
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
