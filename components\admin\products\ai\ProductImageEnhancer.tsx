'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Sparkles, 
  Image, 
  CheckCircle, 
  AlertCircle, 
  Clock, 
  RefreshCw,
  Eye,
  Download,
  Zap
} from 'lucide-react';

interface Product {
  id: string;
  name: string;
  brand: string;
  images: string[];
  enhancementStatus?: string;
  qualityScore?: number;
}

interface EnhancementResult {
  product_name: string;
  image_source: 'web_fetch' | 'enhanced_original';
  images: Array<{
    angle: string;
    url: string;
    qualityScore: number;
    source: string;
  }>;
  confidence_score: number;
  processing_notes: string[];
}

interface ProductImageEnhancerProps {
  product: Product;
  onEnhancementComplete?: (result: EnhancementResult) => void;
}

export default function ProductImageEnhancer({ product, onEnhancementComplete }: ProductImageEnhancerProps) {
  const [isEnhancing, setIsEnhancing] = useState(false);
  const [enhancementResult, setEnhancementResult] = useState<EnhancementResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [progress, setProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState('');

  const handleEnhance = async (autoApprove: boolean = false) => {
    if (!product.images || product.images.length === 0) {
      setError('No images found for this product');
      return;
    }

    setIsEnhancing(true);
    setError(null);
    setProgress(0);
    setCurrentStep('Starting AI enhancement...');

    try {
      // Simulate progress updates
      const progressSteps = [
        { progress: 10, step: 'Analyzing product image...' },
        { progress: 30, step: 'Searching for high-quality images...' },
        { progress: 50, step: 'Applying quality filters...' },
        { progress: 70, step: 'Organizing by viewing angles...' },
        { progress: 90, step: 'Processing and branding images...' },
        { progress: 100, step: 'Enhancement complete!' }
      ];

      // Update progress
      for (const step of progressSteps) {
        setProgress(step.progress);
        setCurrentStep(step.step);
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      const response = await fetch('/api/admin/products/enhance-images', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          originalImageUrl: product.images[0],
          productId: product.id,
          autoApprove,
          confidenceThreshold: 80
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Enhancement failed');
      }

      setEnhancementResult(data.data);
      onEnhancementComplete?.(data.data);

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Enhancement failed');
    } finally {
      setIsEnhancing(false);
      setProgress(0);
      setCurrentStep('');
    }
  };

  const handlePreview = async () => {
    if (!product.images || product.images.length === 0) {
      setError('No images found for this product');
      return;
    }

    setIsEnhancing(true);
    setError(null);
    setCurrentStep('Generating preview...');

    try {
      const response = await fetch('/api/admin/products/preview-enhancement', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          imageUrl: product.images[0]
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Preview generation failed');
      }

      // Show preview in a modal or update state
      console.log('Preview result:', data.data);
      alert(`Preview generated! Found ${data.data.enhancement_preview.total_images_found} enhanced images. Recommendation: ${data.data.enhancement_preview.recommendation.action}`);

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Preview generation failed');
    } finally {
      setIsEnhancing(false);
      setCurrentStep('');
    }
  };

  const getStatusBadge = (status?: string) => {
    switch (status) {
      case 'completed':
        return <Badge variant="default" className="bg-green-500"><CheckCircle className="w-3 h-3 mr-1" />Completed</Badge>;
      case 'processing':
        return <Badge variant="secondary"><Clock className="w-3 h-3 mr-1" />Processing</Badge>;
      case 'review_required':
        return <Badge variant="outline"><AlertCircle className="w-3 h-3 mr-1" />Review Required</Badge>;
      case 'failed':
        return <Badge variant="destructive"><AlertCircle className="w-3 h-3 mr-1" />Failed</Badge>;
      case 'pending':
        return <Badge variant="secondary"><Clock className="w-3 h-3 mr-1" />Pending</Badge>;
      default:
        return <Badge variant="outline">Not Enhanced</Badge>;
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Sparkles className="w-5 h-5 text-purple-500" />
          AI Image Enhancement
        </CardTitle>
        <CardDescription>
          Enhance product images with AI-powered search and processing
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Product Info */}
        <div className="flex items-center justify-between">
          <div>
            <h3 className="font-medium">{product.name}</h3>
            <p className="text-sm text-muted-foreground">{product.brand}</p>
          </div>
          <div className="flex items-center gap-2">
            {getStatusBadge(product.enhancementStatus)}
            {product.qualityScore && (
              <Badge variant="outline">
                Score: {product.qualityScore}%
              </Badge>
            )}
          </div>
        </div>

        {/* Current Images */}
        <div className="flex gap-2 overflow-x-auto">
          {product.images.map((image, index) => (
            <img
              key={index}
              src={image}
              alt={`Product image ${index + 1}`}
              className="w-16 h-16 object-cover rounded border"
            />
          ))}
        </div>

        {/* Progress */}
        {isEnhancing && (
          <div className="space-y-2">
            <Progress value={progress} className="w-full" />
            <p className="text-sm text-muted-foreground">{currentStep}</p>
          </div>
        )}

        {/* Error */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Action Buttons */}
        <div className="flex gap-2">
          <Button
            onClick={handlePreview}
            disabled={isEnhancing}
            variant="outline"
            size="sm"
          >
            <Eye className="w-4 h-4 mr-2" />
            Preview Enhancement
          </Button>
          
          <Button
            onClick={() => handleEnhance(false)}
            disabled={isEnhancing}
            size="sm"
          >
            <Zap className="w-4 h-4 mr-2" />
            Enhance (Manual Review)
          </Button>
          
          <Button
            onClick={() => handleEnhance(true)}
            disabled={isEnhancing}
            variant="default"
            size="sm"
          >
            <Sparkles className="w-4 h-4 mr-2" />
            Enhance & Auto-Apply
          </Button>
        </div>

        {/* Enhancement Results */}
        {enhancementResult && (
          <Tabs defaultValue="results" className="w-full">
            <TabsList>
              <TabsTrigger value="results">Results</TabsTrigger>
              <TabsTrigger value="notes">Processing Notes</TabsTrigger>
            </TabsList>
            
            <TabsContent value="results" className="space-y-4">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">Source:</span> {enhancementResult.image_source}
                </div>
                <div>
                  <span className="font-medium">Confidence:</span> {enhancementResult.confidence_score}%
                </div>
                <div>
                  <span className="font-medium">Images Found:</span> {enhancementResult.images.length}
                </div>
                <div>
                  <span className="font-medium">Product:</span> {enhancementResult.product_name}
                </div>
              </div>
              
              <div className="grid grid-cols-3 gap-2">
                {enhancementResult.images.map((img, index) => (
                  <div key={index} className="text-center">
                    <img
                      src={img.url}
                      alt={`Enhanced ${img.angle}`}
                      className="w-full h-20 object-cover rounded border"
                    />
                    <p className="text-xs mt-1">{img.angle}</p>
                    <p className="text-xs text-muted-foreground">Score: {img.qualityScore}</p>
                  </div>
                ))}
              </div>
            </TabsContent>
            
            <TabsContent value="notes">
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {enhancementResult.processing_notes.map((note, index) => (
                  <p key={index} className="text-sm text-muted-foreground">
                    {note}
                  </p>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        )}
      </CardContent>
    </Card>
  );
}
