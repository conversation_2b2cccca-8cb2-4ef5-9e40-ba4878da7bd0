#!/usr/bin/env node

/**
 * Reset a few products for testing the enhancement system
 */

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function resetForTesting() {
  console.log('🔄 Resetting 5 products for enhancement testing...\n');

  // Get 5 random products
  const products = await prisma.product.findMany({
    take: 5,
    select: {
      id: true,
      name: true,
      brand: true,
      enhancementStatus: true
    }
  });

  console.log('📦 Products to reset:');
  products.forEach((product, index) => {
    console.log(`${index + 1}. ${product.name} (${product.brand}) - Current status: ${product.enhancementStatus}`);
  });

  // Reset their enhancement status
  const productIds = products.map(p => p.id);
  
  await prisma.product.updateMany({
    where: {
      id: { in: productIds }
    },
    data: {
      enhancementStatus: 'pending',
      qualityScore: null
    }
  });

  console.log('\n✅ Reset complete! These products are now ready for enhancement testing.');
  console.log('\n🚀 Next steps:');
  console.log('1. Start your server: pnpm dev');
  console.log('2. Run enhancement: node scripts/quick-enhance.js');

  await prisma.$disconnect();
}

resetForTesting().catch(console.error);
