import { NextRequest, NextResponse } from 'next/server';
import { processProductImages } from '@/utils/image-ai-utils';
import { getCurrentUser } from '@/lib/auth-utils';

export async function POST(request: NextRequest) {
  try {
    // Check authentication and admin role
    const user = await getCurrentUser();
    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { productName, brand, colorway, originalImageUrl } = body;

    // Validate required fields
    if (!productName || !brand || !originalImageUrl) {
      return NextResponse.json(
        { error: 'Missing required fields: productName, brand, originalImageUrl' },
        { status: 400 }
      );
    }

    // Process the product images
    const result = await processProductImages({
      productName,
      brand,
      colorway,
      originalImageUrl
    });

    return NextResponse.json({
      success: true,
      data: result
    });

  } catch (error) {
    console.error('Error processing product images:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to process images' 
      },
      { status: 500 }
    );
  }
}
