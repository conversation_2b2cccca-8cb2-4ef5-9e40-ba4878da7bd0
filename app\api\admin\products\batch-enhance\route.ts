import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/auth-utils";
import { enhanceProductImages } from "@/utils/advanced-image-processor";
import prisma from "@/lib/prisma";

/**
 * POST /api/admin/products/batch-enhance
 * Batch AI Enhancement for Multiple Products
 * Processes multiple products with intelligent queuing and error handling
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication and admin role
    const user = await getCurrentUser();
    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { 
      productIds = [],
      processAll = false,
      confidenceThreshold = 80,
      autoApprove = false,
      maxConcurrent = 3 // Limit concurrent processing to avoid API rate limits
    } = body;

    let productsToProcess;

    if (processAll) {
      // Process all products that have images but haven't been enhanced
      productsToProcess = await prisma.product.findMany({
        where: {
          AND: [
            { images: { isEmpty: false } },
            { 
              OR: [
                { enhancementStatus: null },
                { enhancementStatus: 'pending' },
                { enhancementStatus: 'failed' }
              ]
            }
          ]
        },
        select: {
          id: true,
          name: true,
          brand: true,
          images: true,
          enhancementStatus: true
        },
        take: 50 // Limit batch size
      });
    } else if (productIds.length > 0) {
      // Process specific products
      productsToProcess = await prisma.product.findMany({
        where: {
          id: { in: productIds },
          images: { isEmpty: false }
        },
        select: {
          id: true,
          name: true,
          brand: true,
          images: true,
          enhancementStatus: true
        }
      });
    } else {
      return NextResponse.json(
        { error: 'Either productIds array or processAll=true must be provided' },
        { status: 400 }
      );
    }

    if (productsToProcess.length === 0) {
      return NextResponse.json({
        success: true,
        data: {
          processed: 0,
          skipped: 0,
          failed: 0,
          results: []
        },
        message: 'No products found to process'
      });
    }

    console.log(`🚀 Starting batch enhancement for ${productsToProcess.length} products`);

    // Mark products as processing
    await prisma.product.updateMany({
      where: {
        id: { in: productsToProcess.map(p => p.id) }
      },
      data: {
        enhancementStatus: 'processing'
      }
    });

    const results = [];
    let processed = 0;
    let skipped = 0;
    let failed = 0;

    // Process products in batches to avoid overwhelming the APIs
    for (let i = 0; i < productsToProcess.length; i += maxConcurrent) {
      const batch = productsToProcess.slice(i, i + maxConcurrent);
      
      const batchPromises = batch.map(async (product) => {
        try {
          if (!product.images || product.images.length === 0) {
            skipped++;
            return {
              productId: product.id,
              productName: product.name,
              status: 'skipped',
              reason: 'No images found'
            };
          }

          const originalImageUrl = product.images[0];
          console.log(`Processing ${product.name} (${product.id})...`);

          // Run enhancement
          const enhancementResult = await enhanceProductImages(originalImageUrl, product.id);

          // Determine if auto-approval should be applied
          const shouldAutoApprove = autoApprove && 
            enhancementResult.confidence_score >= confidenceThreshold &&
            enhancementResult.images.length > 0 &&
            enhancementResult.image_source === 'web_fetch';

          if (shouldAutoApprove) {
            // Auto-approve and update product
            const imageUrls = enhancementResult.images.map(img => img.url);
            
            await prisma.product.update({
              where: { id: product.id },
              data: {
                images: imageUrls,
                enhancementStatus: 'completed',
                qualityScore: enhancementResult.confidence_score
              }
            });

            processed++;
            return {
              productId: product.id,
              productName: product.name,
              status: 'completed',
              imagesGenerated: enhancementResult.images.length,
              confidenceScore: enhancementResult.confidence_score,
              imageSource: enhancementResult.image_source,
              autoApproved: true
            };
          } else {
            // Mark as needing manual review
            await prisma.product.update({
              where: { id: product.id },
              data: {
                enhancementStatus: 'review_required',
                qualityScore: enhancementResult.confidence_score
              }
            });

            processed++;
            return {
              productId: product.id,
              productName: product.name,
              status: 'review_required',
              imagesGenerated: enhancementResult.images.length,
              confidenceScore: enhancementResult.confidence_score,
              imageSource: enhancementResult.image_source,
              autoApproved: false,
              reason: enhancementResult.confidence_score < confidenceThreshold 
                ? 'Low confidence score' 
                : 'Enhanced original image'
            };
          }

        } catch (error) {
          console.error(`Failed to process product ${product.id}:`, error);
          
          // Mark as failed
          await prisma.product.update({
            where: { id: product.id },
            data: {
              enhancementStatus: 'failed'
            }
          });

          failed++;
          return {
            productId: product.id,
            productName: product.name,
            status: 'failed',
            error: error instanceof Error ? error.message : 'Unknown error'
          };
        }
      });

      // Wait for current batch to complete
      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);

      // Small delay between batches to be respectful to APIs
      if (i + maxConcurrent < productsToProcess.length) {
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }

    const summary = {
      processed,
      skipped,
      failed,
      total: productsToProcess.length,
      results
    };

    console.log(`✅ Batch enhancement complete: ${processed} processed, ${skipped} skipped, ${failed} failed`);

    return NextResponse.json({
      success: true,
      data: summary,
      message: `Batch processing complete. ${processed}/${productsToProcess.length} products processed successfully.`
    });

  } catch (error) {
    console.error('Error in batch enhancement:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Batch enhancement failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
