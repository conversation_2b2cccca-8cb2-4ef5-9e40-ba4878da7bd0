import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";

/**
 * GET /api/admin/enhancement-stats
 * Get enhancement statistics for the dashboard
 * This endpoint provides basic stats without requiring full admin authentication
 */
export async function GET(request: NextRequest) {
  try {
    // Get basic product counts by enhancement status
    const allProducts = await prisma.product.findMany({
      select: {
        id: true,
        enhancementStatus: true,
        qualityScore: true,
        images: true,
        updatedAt: true
      }
    });

    // Calculate statistics
    const stats = {
      total: allProducts.length,
      completed: allProducts.filter(p => p.enhancementStatus === 'completed').length,
      processing: allProducts.filter(p => p.enhancementStatus === 'processing').length,
      failed: allProducts.filter(p => p.enhancementStatus === 'failed').length,
      pending: allProducts.filter(p => p.enhancementStatus === 'pending' || !p.enhancementStatus).length,
      review_required: allProducts.filter(p => p.enhancementStatus === 'review_required').length,
      with_images: allProducts.filter(p => p.images && p.images.length > 0).length,
      averageQualityScore: 0,
      totalProcessingTime: 0
    };

    // Calculate average quality score
    const productsWithScores = allProducts.filter(p => p.qualityScore);
    if (productsWithScores.length > 0) {
      stats.averageQualityScore = Math.round(
        productsWithScores.reduce((sum, p) => sum + (p.qualityScore || 0), 0) / productsWithScores.length
      );
    }

    // Get recent enhancement logs (last 20 products with enhancement status)
    const recentLogs = allProducts
      .filter(p => p.enhancementStatus)
      .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
      .slice(0, 20)
      .map(p => ({
        id: p.id,
        status: p.enhancementStatus,
        qualityScore: p.qualityScore,
        updatedAt: p.updatedAt,
        hasImages: p.images && p.images.length > 0
      }));

    return NextResponse.json({
      success: true,
      data: {
        stats,
        recentLogs,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Error fetching enhancement stats:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch enhancement statistics',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
