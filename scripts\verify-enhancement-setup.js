#!/usr/bin/env node

/**
 * Verification Script for AI Image Enhancement Setup
 * Checks all required configurations and API connections
 * 
 * Usage:
 * node scripts/verify-enhancement-setup.js
 */

const { PrismaClient } = require('@prisma/client');
const { GoogleGenerativeAI } = require("@google/generative-ai");
const axios = require('axios');

const prisma = new PrismaClient();

async function verifySetup() {
  console.log('🔍 Rivv AI Enhancement Setup Verification');
  console.log('=========================================\n');

  let allGood = true;

  // 1. Check Environment Variables
  console.log('📋 Checking Environment Variables...');
  
  const requiredEnvVars = [
    'GOOGLE_AI_API_KEY',
    'GOOGLE_CUSTOM_SEARCH_API_KEY', 
    'GOOGLE_CUSTOM_SEARCH_ENGINE_ID',
    'UPLOADTHING_SECRET',
    'UPLOADTHING_APP_ID'
  ];

  for (const envVar of requiredEnvVars) {
    if (process.env[envVar]) {
      console.log(`✅ ${envVar}: Configured`);
    } else {
      console.log(`❌ ${envVar}: Missing`);
      allGood = false;
    }
  }

  // 2. Test Gemini AI Connection
  console.log('\n🧠 Testing Gemini AI Connection...');
  try {
    if (!process.env.GOOGLE_AI_API_KEY) {
      console.log('❌ Gemini AI: API key missing');
      allGood = false;
    } else {
      const genAI = new GoogleGenerativeAI(process.env.GOOGLE_AI_API_KEY);
      const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });
      
      const result = await model.generateContent('Test connection - respond with "OK"');
      const response = await result.response;
      
      if (response.text().includes('OK')) {
        console.log('✅ Gemini AI: Connected successfully');
      } else {
        console.log('⚠️  Gemini AI: Connected but unexpected response');
      }
    }
  } catch (error) {
    console.log(`❌ Gemini AI: Connection failed - ${error.message}`);
    allGood = false;
  }

  // 3. Test Google Custom Search
  console.log('\n🔍 Testing Google Custom Search...');
  try {
    const apiKey = process.env.GOOGLE_CUSTOM_SEARCH_API_KEY;
    const searchEngineId = process.env.GOOGLE_CUSTOM_SEARCH_ENGINE_ID;
    
    if (!apiKey || !searchEngineId) {
      console.log('❌ Google Custom Search: API key or Search Engine ID missing');
      allGood = false;
    } else {
      const testUrl = `https://www.googleapis.com/customsearch/v1?key=${apiKey}&cx=${searchEngineId}&q=nike+air+jordan&searchType=image&num=1`;
      
      const response = await axios.get(testUrl);
      
      if (response.data && response.data.items) {
        console.log('✅ Google Custom Search: Working correctly');
        console.log(`   Found ${response.data.items.length} test results`);
        
        // Show search engine info
        if (response.data.searchInformation) {
          console.log(`   Search time: ${response.data.searchInformation.searchTime}s`);
          console.log(`   Total results: ${response.data.searchInformation.totalResults}`);
        }
      } else {
        console.log('⚠️  Google Custom Search: Connected but no results');
      }
    }
  } catch (error) {
    if (error.response?.status === 403) {
      console.log('❌ Google Custom Search: API quota exceeded or invalid credentials');
      console.log('   Check your Google Cloud Console for API quotas and billing');
    } else if (error.response?.status === 400) {
      console.log('❌ Google Custom Search: Invalid search engine ID or API key');
    } else {
      console.log(`❌ Google Custom Search: ${error.message}`);
    }
    allGood = false;
  }

  // 4. Check Database Connection
  console.log('\n🗄️  Testing Database Connection...');
  try {
    const productCount = await prisma.product.count();
    console.log(`✅ Database: Connected (${productCount} products found)`);
    
    // Check for products with images
    const productsWithImages = await prisma.product.count({
      where: { 
        images: { 
          not: { 
            equals: [] 
          } 
        } 
      }
    });
    console.log(`   Products with images: ${productsWithImages}`);
    
    // Check enhancement status distribution
    const allProducts = await prisma.product.findMany({
      select: { enhancementStatus: true }
    });
    
    const statusCounts = {};
    allProducts.forEach(product => {
      const status = product.enhancementStatus || 'not_processed';
      statusCounts[status] = (statusCounts[status] || 0) + 1;
    });
    
    console.log('   Enhancement status distribution:');
    Object.entries(statusCounts).forEach(([status, count]) => {
      console.log(`     ${status}: ${count}`);
    });
    
  } catch (error) {
    console.log(`❌ Database: Connection failed - ${error.message}`);
    allGood = false;
  }

  // 5. Check File System Permissions
  console.log('\n📁 Checking File System...');
  try {
    const fs = require('fs');
    const path = require('path');
    
    // Check if temp directory exists and is writable
    const tempDir = path.join(process.cwd(), 'temp');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }
    
    // Test write permissions
    const testFile = path.join(tempDir, 'test-write.txt');
    fs.writeFileSync(testFile, 'test');
    fs.unlinkSync(testFile);
    
    console.log('✅ File System: Write permissions OK');
  } catch (error) {
    console.log(`❌ File System: Write permission issues - ${error.message}`);
    allGood = false;
  }

  // 6. Check Sharp.js (Image Processing)
  console.log('\n🖼️  Testing Image Processing...');
  try {
    const sharp = require('sharp');
    
    // Create a simple test image
    const testBuffer = await sharp({
      create: {
        width: 100,
        height: 100,
        channels: 3,
        background: { r: 255, g: 255, b: 255 }
      }
    }).jpeg().toBuffer();
    
    if (testBuffer.length > 0) {
      console.log('✅ Sharp.js: Image processing working');
    }
  } catch (error) {
    console.log(`❌ Sharp.js: Image processing failed - ${error.message}`);
    console.log('   Try: pnpm install sharp');
    allGood = false;
  }

  // 7. Check if Next.js server is running (optional)
  console.log('\n🚀 Testing Enhancement API...');
  try {
    const response = await fetch('http://localhost:3000/api/admin/products/enhance-images', {
      method: 'GET'
    });
    
    if (response.ok) {
      console.log('✅ Enhancement API: Endpoint accessible');
    } else {
      console.log(`⚠️  Enhancement API: Endpoint returned ${response.status}`);
    }
  } catch (error) {
    console.log('⚠️  Enhancement API: Could not test (server may not be running)');
    console.log('   Start your Next.js server with: pnpm dev');
  }

  // Final Summary
  console.log('\n📊 Setup Verification Summary');
  console.log('=============================');
  
  if (allGood) {
    console.log('🎉 All systems are GO! Your AI enhancement system is ready.');
    console.log('\n🚀 Ready to run:');
    console.log('   • node scripts/quick-enhance.js (test with 5 products)');
    console.log('   • node scripts/enhance-all-existing-products.js --dry-run (preview all)');
    console.log('   • node scripts/enhance-all-existing-products.js (process all)');
  } else {
    console.log('⚠️  Some issues found. Please fix the ❌ items above before running enhancement.');
    console.log('\n🔧 Common fixes:');
    console.log('   • Add missing environment variables to .env file');
    console.log('   • Check Google API quotas and billing');
    console.log('   • Verify database connection string');
    console.log('   • Install missing dependencies: pnpm install');
  }

  await prisma.$disconnect();
}

verifySetup().catch(console.error).finally(() => process.exit(0));
