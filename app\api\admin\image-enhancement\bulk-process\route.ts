import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth-utils';
import { spawn } from 'child_process';
import path from 'path';

export async function POST(request: NextRequest) {
  try {
    // Check authentication and admin role
    const user = await getCurrentUser();
    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { batchSize = 10, productId, force = false } = body;

    // Validate batch size
    if (batchSize < 1 || batchSize > 50) {
      return NextResponse.json(
        { error: 'Batch size must be between 1 and 50' },
        { status: 400 }
      );
    }

    // Build command arguments
    const args = [
      'scripts/enhance-existing-products.js',
      `--batch-size=${batchSize}`
    ];

    if (productId) {
      args.push(`--product-id=${productId}`);
    }

    if (force) {
      args.push('--force');
    }

    // Start the enhancement process in the background
    const scriptPath = path.join(process.cwd(), 'scripts/enhance-existing-products.js');
    
    try {
      const child = spawn('node', [scriptPath, ...args.slice(1)], {
        detached: true,
        stdio: 'ignore',
        cwd: process.cwd(),
        env: { ...process.env }
      });

      // Detach the process so it continues running
      child.unref();

      return NextResponse.json({
        success: true,
        message: 'Bulk enhancement process started successfully',
        processId: child.pid,
        batchSize,
        productId: productId || null
      });

    } catch (spawnError) {
      console.error('Error spawning enhancement process:', spawnError);
      return NextResponse.json(
        { error: 'Failed to start enhancement process' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error starting bulk enhancement:', error);
    return NextResponse.json(
      { error: 'Failed to start bulk enhancement process' },
      { status: 500 }
    );
  }
}
